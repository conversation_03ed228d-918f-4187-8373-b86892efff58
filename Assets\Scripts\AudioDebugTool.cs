using UnityEngine;
using SIPSorcery.Media;

/// <summary>
/// 音频调试工具 - 用于测试和验证音频播放修复
/// </summary>
public class AudioDebugTool : MonoBehaviour
{
    [Header("调试控制")]
    public bool enableDebugLogs = true;
    public bool autoCheckEnvironment = true;
    
    [Header("测试控制")]
    public KeyCode checkEnvironmentKey = KeyCode.F1;
    public KeyCode generateTestAudioKey = KeyCode.F2;
    public KeyCode showStatsKey = KeyCode.F3;
    
    private AudioExtrasSink _audioSink;
    private AudioManager _audioManager;
    
    void Start()
    {
        // 查找音频组件
        _audioSink = FindObjectOfType<AudioExtrasSink>();
        _audioManager = FindObjectOfType<AudioManager>();
        
        if (_audioSink == null)
        {
            Debug.LogWarning("[AudioDebugTool] 未找到AudioExtrasSink组件");
        }
        
        if (_audioManager == null)
        {
            Debug.LogWarning("[AudioDebugTool] 未找到AudioManager组件");
        }
        
        // 自动检查环境
        if (autoCheckEnvironment && _audioSink != null)
        {
            Invoke(nameof(CheckEnvironmentDelayed), 2.0f); // 延迟2秒检查
        }
        
        // 显示使用说明
        Debug.Log("[AudioDebugTool] 音频调试工具已启动");
        Debug.Log($"  - 按 {checkEnvironmentKey} 检查音频环境");
        Debug.Log($"  - 按 {generateTestAudioKey} 生成测试音频");
        Debug.Log($"  - 按 {showStatsKey} 显示统计信息");
    }
    
    void Update()
    {
        if (_audioSink == null) return;
        
        // 键盘快捷键
        if (Input.GetKeyDown(checkEnvironmentKey))
        {
            CheckAudioEnvironment();
        }
        
        if (Input.GetKeyDown(generateTestAudioKey))
        {
            GenerateTestAudio();
        }
        
        if (Input.GetKeyDown(showStatsKey))
        {
            ShowAudioStats();
        }
    }
    
    /// <summary>
    /// 延迟检查音频环境
    /// </summary>
    private void CheckEnvironmentDelayed()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 自动检查音频环境...");
            _audioSink.CheckAudioEnvironment();
        }
    }
    
    /// <summary>
    /// 检查音频环境
    /// </summary>
    public void CheckAudioEnvironment()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 手动检查音频环境...");
            _audioSink.CheckAudioEnvironment();
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法检查环境");
        }
    }
    
    /// <summary>
    /// 生成测试音频
    /// </summary>
    public void GenerateTestAudio()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 生成测试音频信号...");
            _audioSink.GenerateTestAudio();
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法生成测试音频");
        }
    }
    
    /// <summary>
    /// 显示音频统计信息
    /// </summary>
    public void ShowAudioStats()
    {
        Debug.Log("[AudioDebugTool] 音频统计信息:");
        
        // Unity音频设置
        Debug.Log($"  Unity音频设置:");
        Debug.Log($"    - 输出采样率: {AudioSettings.outputSampleRate}Hz");
        Debug.Log($"    - DSP缓冲区: {AudioSettings.GetConfiguration().dspBufferSize}");
        Debug.Log($"    - 扬声器模式: {AudioSettings.GetConfiguration().speakerMode}");
        
        // AudioManager状态
        if (_audioManager != null)
        {
            Debug.Log($"  AudioManager状态:");
            Debug.Log($"    - 音频会话已启动: {_audioManager.IsAudioSessionStarted()}");
        }
        
        // AudioSource状态
        var audioSources = FindObjectsOfType<AudioSource>();
        Debug.Log($"  场景中的AudioSource数量: {audioSources.Length}");
        
        foreach (var source in audioSources)
        {
            if (source.gameObject.name.Contains("SIP") || source.clip?.name.Contains("SIP") == true)
            {
                Debug.Log($"    SIP AudioSource: {source.gameObject.name}");
                Debug.Log($"      - 播放状态: {source.isPlaying}");
                Debug.Log($"      - 音量: {source.volume}");
                Debug.Log($"      - 优先级: {source.priority}");
                if (source.clip != null)
                {
                    Debug.Log($"      - Clip: {source.clip.name}, {source.clip.frequency}Hz, {source.clip.length:F2}s");
                }
            }
        }
    }
    
    /// <summary>
    /// GUI显示调试信息
    /// </summary>
    void OnGUI()
    {
        if (!enableDebugLogs) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.BeginVertical("box");
        
        GUILayout.Label("音频调试工具", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("检查音频环境"))
        {
            CheckAudioEnvironment();
        }
        
        if (GUILayout.Button("生成测试音频"))
        {
            GenerateTestAudio();
        }
        
        if (GUILayout.Button("显示统计信息"))
        {
            ShowAudioStats();
        }
        
        GUILayout.Space(10);
        
        // 显示基本状态
        GUILayout.Label($"Unity采样率: {AudioSettings.outputSampleRate}Hz");
        
        if (_audioManager != null)
        {
            GUILayout.Label($"音频会话: {(_audioManager.IsAudioSessionStarted() ? "已启动" : "未启动")}");
        }
        
        var sipAudioSources = System.Array.FindAll(FindObjectsOfType<AudioSource>(), 
            s => s.gameObject.name.Contains("SIP") || (s.clip?.name.Contains("SIP") == true));
        GUILayout.Label($"SIP AudioSource: {sipAudioSources.Length}个");
        
        if (sipAudioSources.Length > 0)
        {
            var source = sipAudioSources[0];
            GUILayout.Label($"播放状态: {(source.isPlaying ? "播放中" : "已停止")}");
            GUILayout.Label($"音量: {source.volume:F2}");
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
