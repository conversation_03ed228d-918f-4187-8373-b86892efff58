# Unity SIP 2.0 实时音频通信技术深度分析

## 文档概述

本文档基于Unity SIP 2.0项目的架构图和核心代码，深入分析SIP实时音频通信的完整技术流程，涵盖SIP协议建立、RTP音频流处理、编解码器集成、架构组件交互等关键技术环节。

## 目录

1. [SIP协议建立阶段的技术细节](#1-sip协议建立阶段的技术细节)
2. [RTP音频流的端到端处理流程](#2-rtp音频流的端到端处理流程)
3. [SIPSorceryMedia.FFmpeg集成的技术实现](#3-sipsorcerymediaffmpeg集成的技术实现)
4. [架构组件间的数据流和控制流分析](#4-架构组件间的数据流和控制流分析)
5. [关键组件代码示例和配置参数](#5-关键组件代码示例和配置参数)
6. [性能优化建议](#6-性能优化建议)

---

## 1. SIP协议建立阶段的技术细节

### 1.1 SIP会话建立的完整消息序列

SIP呼叫建立遵循标准的三次握手协议，由`SIPSignalingManager.cs`负责处理：

**完整消息序列**：
1. **INVITE请求**：携带本地SDP Offer，包含支持的音频编解码器列表
2. **100 Trying**：服务器临时响应，表示正在处理请求
3. **180 Ringing**：触发`ClientCallRinging`事件，更新UI状态为振铃
4. **200 OK**：携带远程SDP Answer，触发`ClientCallAnswered`事件
5. **ACK确认**：完成三次握手，建立SIP对话

**核心实现代码**：
```csharp
public async Task<bool> Call(string destination, string sipUsername, string sipPassword, VoIPMediaSession mediaSession, int timeout)
{
    try
    {
        Debug.Log($"[SIP] 开始呼叫流程 - 目标: {destination}");

        _currentMediaSession = mediaSession;

        // 构建 From Header
        var fromSipUri = SIPURI.ParseSIPURIRelaxed($"sip:{sipUsername}@{this.sipDomain}");
        var fromHeader = new SIPFromHeader(Environment.MachineName, fromSipUri, CallProperties.CreateNewTag());

        // 构建 Contact Header
        var contactUri = new SIPURI(sipUsername, $"{localBoundEp.Address}:{localBoundEp.Port}", "transport=tcp");

        // 调用 SIPUserAgent.Call 发起呼叫
        var callResult = await _userAgent.Call(destination, sipUsername, sipPassword, mediaSession, timeout);

        return callResult;
    }
    catch (Exception ex)
    {
        Debug.LogError($"[SIP] 发起呼叫失败: {ex.Message}");
        return false;
    }
}
```

### 1.2 SDP协商过程中音频编解码器的选择算法

`CodecNegotiationManager.cs`实现了智能的编解码器协商机制：

**编解码器优先级机制**：
```csharp
private void InitializeDefaultPriorities()
{
    // 音频编解码器优先级（按质量和兼容性排序）
    _audioCodecPriorities.AddRange(new[]
    {
        new CodecPriority { AudioCodec = AudioCodecsEnum.G722, Priority = 1, Description = "G.722 - 高质量音频" },
        new CodecPriority { AudioCodec = AudioCodecsEnum.PCMU, Priority = 2, Description = "G.711 μ-law - 标准兼容性" },
        new CodecPriority { AudioCodec = AudioCodecsEnum.PCMA, Priority = 3, Description = "G.711 A-law - 欧洲标准" },
        new CodecPriority { AudioCodec = AudioCodecsEnum.OPUS, Priority = 4, Description = "Opus - 现代高效编码（待实现）" }
    });
}
```

**协商算法流程**：
1. **格式收集**：从本地和远程SDP中提取支持的音频格式
2. **优先级匹配**：按优先级顺序查找兼容的编解码器
3. **兼容性检查**：通过Payload Type和格式名称进行匹配验证
4. **结果应用**：将协商结果应用到音频管理器

### 1.3 NAT穿透和ICE候选者收集

项目使用SIPSorcery库的内置NAT穿透机制：
- **本地端点管理**：通过`localBoundEp`管理本地网络绑定
- **传输协议选择**：支持TCP/UDP传输协议的自动选择
- **ICE处理**：由SIPSorcery库内部处理ICE候选者收集和连通性检查

---

## 2. RTP音频流的端到端处理流程

### 2.1 音频采集与编码端

#### Unity AudioSource/Microphone数据采集

`AudioExtrasSource.cs`实现了精确的音频采集机制：

**采集配置参数**：
- **采样率**：16kHz（G.722标准）
- **帧大小**：320样本（20ms @ 16kHz）
- **缓冲区**：循环缓冲，1秒长度
- **采集周期**：精确20ms间隔控制

**核心采集协程**：
```csharp
private System.Collections.IEnumerator AudioProcessingCoroutine()
{
    float targetInterval = 0.02f; // 20ms目标间隔
    float nextProcessTime = Time.realtimeSinceStartup + targetInterval;

    while (true)
    {
        if (_isStarted && _microphoneClip != null)
        {
            // 获取麦克风数据
            int currentPosition = Microphone.GetPosition(Microphone.devices[0]);
            int available = currentPosition - _lastPosition;

            if (available >= BUFFER_SIZE) // 320样本
            {
                _microphoneClip.GetData(_microphoneBuffer, _lastPosition);

                // 音频处理：噪声门限、增益控制、VAD
                ProcessAudioData(_microphoneBuffer);

                // G.722编码
                int encodedBytes = _g722Encoder.Encode(_g722EncoderState, _g722EncodeBuffer, pcmData, pcmData.Length);

                // 发送RTP包
                OnAudioSourceEncodedSample?.Invoke(_audioTimestamp, encodedSample);

                // 推进时间戳
                _audioTimestamp += 160; // G.722 RTP时钟速率8kHz，20ms = 160时钟周期
            }
        }

        // 精确时序控制
        while (Time.realtimeSinceStartup < nextProcessTime)
        {
            yield return null;
        }
        nextProcessTime += targetInterval;
    }
}
```

#### 音频数据格式转换技术细节

**转换流程**：
1. **Unity float[] → PCM short[]**：`pcmData[i] = (short)(_microphoneBuffer[i] * 32767f)`
2. **音频增强处理**：
   - 噪声门限：`NoiseGateThreshold = 0.005f`
   - 增益控制：`MicrophoneGain = 1.0f`
   - 压缩处理：动态范围压缩
   - VAD检测：语音活动检测算法

#### G.722编解码器配置和性能特点

**G.722技术参数**：
- **采样率**：16kHz（解码后PCM）
- **RTP时钟速率**：8kHz（标准规定）
- **压缩比**：2:1（320样本 → 160字节）
- **Payload Type**：9
- **比特率**：64kbps
- **音频质量**：宽带音频，频响范围50Hz-7kHz

**性能优势**：
- 相比G.711提供更好的音频质量
- 保持良好的网络带宽效率
- 广泛的设备兼容性
- 低延迟编解码处理

#### RTP包封装过程

**关键参数设置**：
```csharp
// RTP包头参数
private uint _audioSsrc;           // 随机生成的同步源标识符
private ushort _sequenceNumber;    // 递增的16位序列号
private uint _audioTimestamp;      // 基于8kHz时钟的时间戳

// 时间戳计算
_audioTimestamp += 160; // 每20ms增加160个时钟周期
```

### 2.2 网络传输层

#### RTP/RTCP协议栈实现

**核心特性**：
- **RTP会话管理**：通过`VoIPMediaSession`统一管理
- **SSRC管理**：自动生成和冲突检测
- **序列号处理**：递增序列号和回绕处理
- **时间戳同步**：精确的时间戳计算和同步

#### 网络抖动缓冲和丢包重传机制

**抖动缓冲实现**：
```csharp
// 简化的音频队列缓冲
private Queue<float[]> _audioQueue = new Queue<float[]>();
private const int MAX_QUEUE_SIZE = 50; // 最大队列大小（1秒音频）

// 丢包检测算法
if (_firstPacketReceived)
{
    uint expectedSeq = (_lastSequenceNumber + 1) & 0xFFFF;
    if (seqnum != expectedSeq)
    {
        uint lostPackets = (seqnum - expectedSeq) & 0xFFFF;
        if (lostPackets < 100) // 防止序列号回绕导致的误判
        {
            Debug.LogWarning($"检测到丢包: 期望={expectedSeq}, 实际={seqnum}, 丢失={lostPackets}帧");
        }
    }
}
```

### 2.3 音频解码与播放端

#### RTP包接收和重排序算法

`AudioExtrasSink.cs`实现了高效的音频接收处理：

```csharp
public void GotAudioRtp(System.Net.IPEndPoint remoteEndPoint, uint ssrc, uint seqnum, uint timestamp, int payloadType, bool marker, byte[] payload)
{
    if (!_isStarted || payload == null || payload.Length == 0) return;

    // 丢包检测
    if (_firstPacketReceived)
    {
        uint expectedSeq = (_lastSequenceNumber + 1) & 0xFFFF;
        if (seqnum != expectedSeq)
        {
            uint lostPackets = (seqnum - expectedSeq) & 0xFFFF;
            Debug.LogWarning($"检测到丢包: 期望={expectedSeq}, 实际={seqnum}, 丢失={lostPackets}帧");
        }
    }

    _lastSequenceNumber = seqnum;

    // G.722解码
    var decodedSamples = new short[payload.Length * 2];
    int decodedLength = _g722Codec.Decode(_g722Decoder, decodedSamples, payload, payload.Length);

    if (decodedLength > 0)
    {
        // 转换为Unity音频格式
        var floatData = new float[decodedLength];
        for (int i = 0; i < decodedLength; i++)
        {
            floatData[i] = decodedSamples[i] / 32768.0f;
        }

        // 加入播放队列
        lock (_queueLock)
        {
            _audioQueue.Enqueue(floatData);

            // 限制队列大小
            while (_audioQueue.Count > MAX_QUEUE_SIZE)
            {
                _audioQueue.Dequeue();
            }
        }
    }
}
```

#### Unity AudioClip动态生成和实时播放

**播放系统实现**：
```csharp
// Unity音频回调 - 直接从队列读取音频数据
private void OnAudioRead(float[] data)
{
    lock (_queueLock)
    {
        if (_audioQueue.Count > 0)
        {
            float[] audioFrame = _audioQueue.Dequeue();

            // 复制音频数据到Unity缓冲区
            int samplesToCopy = Math.Min(audioFrame.Length, data.Length);
            Array.Copy(audioFrame, 0, data, 0, samplesToCopy);

            // 如果Unity请求的样本数多于我们有的，用静音填充
            if (data.Length > samplesToCopy)
            {
                Array.Clear(data, samplesToCopy, data.Length - samplesToCopy);
            }
        }
        else
        {
            // 队列为空，输出静音
            Array.Clear(data, 0, data.Length);
        }
    }
}
```

**技术特点**：
- **实时播放**：使用Unity的OnAudioRead回调实现零延迟播放
- **采样率匹配**：16kHz播放采样率匹配G.722解码输出
- **缓冲区管理**：动态缓冲区大小调整
- **线程安全**：使用锁机制保证多线程安全

---