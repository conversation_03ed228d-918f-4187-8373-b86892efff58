using System;
using System.Collections.Generic;
using SIPSorceryMedia.Abstractions;
using UnityEngine;
using SIPSorcery.Media;
using System.Linq;
using System.IO;

namespace SIPSorcery.Media
{
    /// <summary>
    /// SIP音频接收器（G.722解码+Unity播放）- 完全简化版本
    /// 直接播放8000Hz音频，无环形缓冲区，无重采样，无复杂缓冲算法
    /// </summary>
    public class AudioExtrasSink : MonoBehaviour, IAudioSink
    {
        private const int G722_PCM_SAMPLE_RATE = 16000; // G.722 PCM采样率 (解码后)
        private const int G722_RTP_CLOCK_RATE = 8000; // G.722 RTP时钟频率 (标准规定)
        private const int CHANNELS = 1;
        private const int PCM_FRAME_SAMPLES = 320; // 20ms G.722 PCM采样点数 (16000 * 0.02)
        private const int G722_FRAME_SIZE = 160; // 20ms G.722字节数（压缩比2:1）

        // Unity播放配置
        private int _unityPlaybackSampleRate;
        private AudioSource _audioSource;
        private float _volume = 1.0f;
        private bool _needsResampling = false;

        // 音频状态监控
        private float _lastAudioPlayTime = 0f;
        private bool _hasPlayedAudio = false;
        private int _currentQueueSize = 0;

        // 音频统计
        private struct AudioStats
        {
            public int PacketsReceived;
            public int FramesDecoded;
            public int FramesDropped;
            public int DecodeErrors;
            public int AudioReadCalls;
            public int AudioFrames;
            public int SilentFrames;
            public int PacketLoss;
            public float MaxAudioLevel;
            public float AvgAudioLevel;
        }
        private AudioStats _stats = new AudioStats();


        // 🔥 高性能环形缓冲区 - 替代Queue<float[]>
        private float[] _pcmRingBuffer;
        private volatile int _writeIndex = 0;
        private volatile int _readIndex = 0;
        private readonly object _bufferLock = new object();

        // 缓冲区配置
        private const int RING_BUFFER_SECONDS = 2; // 2秒缓冲区
        private int _ringBufferSize; // 运行时计算
        private int _frameSize; // 单帧样本数（20ms）

        /// <summary>
        /// 获取环形缓冲区可用空间（样本数）
        /// </summary>
        private int GetAvailableSpace()
        {
            int space = _readIndex - _writeIndex - 1;
            if (space < 0) space += _ringBufferSize;
            return space;
        }

        /// <summary>
        /// 获取环形缓冲区已用空间（样本数）
        /// </summary>
        private int GetUsedSpace()
        {
            int used = _writeIndex - _readIndex;
            if (used < 0) used += _ringBufferSize;
            return used;
        }

        /// <summary>
        /// 写入音频数据到环形缓冲区
        /// </summary>
        private bool WriteToRingBuffer(float[] data)
        {
            if (data == null || data.Length == 0) return false;

            lock (_bufferLock)
            {
                int availableSpace = GetAvailableSpace();

                // 检查是否有足够空间
                if (availableSpace < data.Length)
                {
                    // 缓冲区满，丢弃最老的数据
                    int samplesToSkip = data.Length - availableSpace + _frameSize;
                    _readIndex = (_readIndex + samplesToSkip) % _ringBufferSize;
                    _stats.FramesDropped++;

                    if (_stats.FramesDropped % 10 == 1)
                    {
                        Debug.LogWarning($"[AudioExtrasSink] 🔥 环形缓冲区满，跳过{samplesToSkip}样本");
                    }
                }

                // 写入数据
                for (int i = 0; i < data.Length; i++)
                {
                    _pcmRingBuffer[_writeIndex] = data[i];
                    _writeIndex = (_writeIndex + 1) % _ringBufferSize;
                }

                return true;
            }
        }

        /// <summary>
        /// 从环形缓冲区读取音频数据
        /// </summary>
        private int ReadFromRingBuffer(float[] data)
        {
            if (data == null || data.Length == 0) return 0;

            lock (_bufferLock)
            {
                int usedSpace = GetUsedSpace();
                int samplesToRead = Math.Min(data.Length, usedSpace);

                // 读取数据
                for (int i = 0; i < samplesToRead; i++)
                {
                    data[i] = _pcmRingBuffer[_readIndex];
                    _readIndex = (_readIndex + 1) % _ringBufferSize;
                }

                // 剩余部分填充静音
                if (samplesToRead < data.Length)
                {
                    Array.Clear(data, samplesToRead, data.Length - samplesToRead);
                }

                return samplesToRead;
            }
        }

        // 基本状态
        private bool _isStarted = false;
        private uint _lastSequenceNumber = 0;
        private bool _firstPacketReceived = false;

        // G.722解码器
        private G722Codec _g722Codec;
        private G722CodecState _g722Decoder;

        // 音频保存相关变量
        private bool _enableAudioSaving = true;
        private string _audioSavePath;
        private FileStream _decodedAudioFile;
        private FileStream _playbackAudioFile;
        private BinaryWriter _decodedWriter;
        private BinaryWriter _playbackWriter;
        private int _savedDecodedSamples = 0;
        private int _savedPlaybackSamples = 0;
        private const int MAX_SAVE_SAMPLES = 8000 * 30; // 最多保存30秒音频
        private bool _audioSavingInitialized = false;

        public event SourceErrorDelegate OnError = delegate { };
        public event SourceErrorDelegate OnTimeout = delegate { };
        public event SourceErrorDelegate OnAudioSinkError = delegate { };

        public void SetUnityAudioSource(AudioSource audioSource)
        {
            _audioSource = audioSource;
            if (_audioSource != null)
            {
                StartCoroutine(DelayedInitialization());
            }
        }

        /// <summary>
        /// 延迟初始化，确保在MediaManager完成音频设置后再读取采样率 - 修复版本
        /// </summary>
        private System.Collections.IEnumerator DelayedInitialization()
        {
            yield return null;

            // 获取Unity系统采样率
            int systemSampleRate = AudioSettings.outputSampleRate;
            Debug.Log($"[AudioExtrasSink] Unity系统采样率: {systemSampleRate}Hz");
            Debug.Log($"[AudioExtrasSink] G.722解码输出采样率: {G722_PCM_SAMPLE_RATE}Hz");

            // 智能采样率适配策略
            if (systemSampleRate >= 44100) // 高质量系统
            {
                _unityPlaybackSampleRate = systemSampleRate;
                _needsResampling = true;
                Debug.Log($"[AudioExtrasSink] 启用重采样: {G722_PCM_SAMPLE_RATE}Hz → {systemSampleRate}Hz");
            }
            else if (systemSampleRate == G722_PCM_SAMPLE_RATE) // 完美匹配
            {
                _unityPlaybackSampleRate = G722_PCM_SAMPLE_RATE;
                _needsResampling = false;
                Debug.Log($"[AudioExtrasSink] 采样率完美匹配: {G722_PCM_SAMPLE_RATE}Hz");
            }
            else // 其他情况，使用系统采样率
            {
                _unityPlaybackSampleRate = systemSampleRate;
                _needsResampling = true;
                Debug.LogWarning($"[AudioExtrasSink] 非标准采样率，启用重采样: {G722_PCM_SAMPLE_RATE}Hz → {systemSampleRate}Hz");
            }

            Debug.Log($"[AudioExtrasSink] Unity DSP缓冲区大小: {AudioSettings.GetConfiguration().dspBufferSize}样本");

            // 🔥 初始化环形缓冲区
            _frameSize = (_unityPlaybackSampleRate * 20) / 1000; // 20ms帧大小
            _ringBufferSize = _unityPlaybackSampleRate * RING_BUFFER_SECONDS; // 2秒缓冲区
            _pcmRingBuffer = new float[_ringBufferSize];
            _writeIndex = 0;
            _readIndex = 0;

            Debug.Log($"[AudioExtrasSink] 🔥 环形缓冲区初始化:");
            Debug.Log($"  - 缓冲区大小: {_ringBufferSize}样本 ({RING_BUFFER_SECONDS}秒)");
            Debug.Log($"  - 帧大小: {_frameSize}样本 (20ms)");
            Debug.Log($"  - 最大帧数: {_ringBufferSize / _frameSize}帧");

            // 配置AudioSource - 修复版本
            if (_audioSource != null)
            {
                _audioSource.playOnAwake = false;
                _audioSource.loop = true;
                _audioSource.spatialBlend = 0f; // 2D音频
                _audioSource.pitch = 1.0f;
                _audioSource.volume = _volume;
                _audioSource.priority = 64; // 中等优先级，不抢占其他重要音频
                _audioSource.bypassEffects = true; // 绕过效果以减少延迟
                _audioSource.bypassListenerEffects = true;
                _audioSource.bypassReverbZones = true;
                _audioSource.Stop();

                // 创建AudioClip，使用适配后的采样率
                int clipLengthSamples = _unityPlaybackSampleRate; // 1秒缓冲区
                _audioSource.clip = AudioClip.Create("SIPAudioPlayback", clipLengthSamples, CHANNELS, _unityPlaybackSampleRate, true, OnAudioRead, OnAudioSetPosition);

                Debug.Log($"[AudioExtrasSink] AudioClip创建成功:");
                Debug.Log($"  - 采样率: {_unityPlaybackSampleRate}Hz");
                Debug.Log($"  - 长度: {clipLengthSamples}样本 (1秒)");
                Debug.Log($"  - 声道: {CHANNELS}");
                Debug.Log($"  - 需要重采样: {_needsResampling}");

                _audioSource.Play();
                Debug.Log($"[AudioExtrasSink] AudioSource播放状态: {_audioSource.isPlaying}");

                Debug.Log($"[AudioExtrasSink] 音频配置完成 - 直接播放8000Hz音频");
            }


            // 初始化音频保存功能
            InitializeAudioSaving();

            // 启动音频状态监控
            StartCoroutine(AudioStatusMonitor());
        }

        public void SetVolume(float volume)
        {
            _volume = Mathf.Clamp01(volume);
            if (_audioSource != null)
            {
                _audioSource.volume = _volume;
            }
        }

        public void Start()
        {
            if (_isStarted) return;

            // 初始化G.722解码器
            try
            {
                _g722Codec = new G722Codec();
                _g722Decoder = new G722CodecState(64000, G722Flags.None);
                Debug.Log("[AudioExtrasSink] G.722解码器初始化成功");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] G.722解码器初始化失败: {ex.Message}");
                return;
            }

            _isStarted = true;
            _firstPacketReceived = false;
            _lastSequenceNumber = 0;

            // 🔥 清空环形缓冲区
            lock (_bufferLock)
            {
                _writeIndex = 0;
                _readIndex = 0;
                if (_pcmRingBuffer != null)
                {
                    Array.Clear(_pcmRingBuffer, 0, _pcmRingBuffer.Length);
                }
            }

            Debug.Log("[AudioExtrasSink] 音频接收器已启动");
        }

        public void Close()
        {
            if (!_isStarted) return;

            _isStarted = false;

            // 关闭音频保存功能
            CloseAudioSaving();

            // 清理G.722解码器
            try
            {
                // G722CodecState不需要显式释放，只需要设置为null
                _g722Decoder = null;
                _g722Codec = null;
                Debug.Log("[AudioExtrasSink] G.722解码器已清理");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] G.722解码器清理失败: {ex.Message}");
            }

            // 🔥 清空环形缓冲区
            lock (_bufferLock)
            {
                _writeIndex = 0;
                _readIndex = 0;
                if (_pcmRingBuffer != null)
                {
                    Array.Clear(_pcmRingBuffer, 0, _pcmRingBuffer.Length);
                }
            }

            // 停止音频播放
            _audioSource?.Stop();

            Debug.Log("[AudioExtrasSink] 音频接收器已关闭");
        }

        /// <summary>
        /// RTP解包后调用，输入G.722音频帧 - 修复版本
        /// </summary>
        public void GotAudioRtp(System.Net.IPEndPoint remoteEndPoint, uint ssrc, uint seqnum, uint timestamp, int payloadType, bool marker, byte[] payload)
        {
            if (!_isStarted || payload == null || payload.Length == 0)
            {
                if (payload == null || payload.Length == 0)
                {
                    Debug.LogWarning($"[AudioExtrasSink] RTP包被拒绝: started={_isStarted}, payload={(payload?.Length ?? 0)}字节");
                }
                return;
            }

            // 检测丢包
            if (_firstPacketReceived)
            {
                uint expectedSeq = (_lastSequenceNumber + 1) & 0xFFFF;
                if (seqnum != expectedSeq)
                {
                    uint lostPackets = (seqnum - expectedSeq) & 0xFFFF;
                    if (lostPackets < 100) // 防止序 列号回绕导致的误判
                    {
                        Debug.LogWarning($"[AudioExtrasSink] 检测到丢包: 期望={expectedSeq}, 实际={seqnum}, 丢失={lostPackets}帧");
                        _stats.PacketLoss += (int)lostPackets;
                    }
                }
            }
            else
            {
                _firstPacketReceived = true;
                Debug.Log($"[AudioExtrasSink] 接收到第一个RTP包: Seq={seqnum}, PayloadType={payloadType}, Size={payload.Length}字节");
            }

            _lastSequenceNumber = seqnum;
            _stats.PacketsReceived++;

            // 使用G.722解码器解码音频数据 - 修复版本
            try
            {
                // G.722解码：payload字节 -> PCM样本 (压缩比约2:1)
                var decodedSamples = new short[payload.Length * 2];
                int decodedLength = _g722Codec.Decode(_g722Decoder, decodedSamples, payload, payload.Length);

                if (decodedLength > 0)
                {
                    // 只使用实际解码的样本
                    var actualDecoded = new short[decodedLength];
                    Array.Copy(decodedSamples, actualDecoded, decodedLength);

                    // 修复：正确的PCM到float转换
                    var floatData = new float[decodedLength];
                    for (int i = 0; i < decodedLength; i++)
                    {
                        // 使用正确的除数和范围限制
                        float sample = actualDecoded[i] / 32767.0f; // 16位有符号整数最大值
                        floatData[i] = Mathf.Clamp(sample, -1.0f, 1.0f);
                    }

                    // 验证音频数据质量
                    ValidateAudioData(floatData, seqnum);

                    // 如果需要重采样，进行重采样处理
                    if (_needsResampling)
                    {
                        floatData = ResampleAudio(floatData, G722_PCM_SAMPLE_RATE, _unityPlaybackSampleRate);
                    }

                    // 🔥 写入环形缓冲区 - 高性能版本
                    bool writeSuccess = WriteToRingBuffer(floatData);

                    if (writeSuccess)
                    {
                        // 更新队列大小统计（用于兼容性）
                        _currentQueueSize = GetUsedSpace() / _frameSize;

                        // 调试：显示缓冲区状态变化
                        if (_stats.PacketsReceived <= 10 || _stats.PacketsReceived % 50 == 0)
                        {
                            int usedSpace = GetUsedSpace();
                            int availableSpace = GetAvailableSpace();
                            Debug.Log($"[AudioExtrasSink] 🔥 环形缓冲区: 已用={usedSpace}样本({usedSpace/_frameSize}帧), 可用={availableSpace}样本, Seq={seqnum}");
                        }
                    }
                    else
                    {
                        Debug.LogError($"[AudioExtrasSink] 环形缓冲区写入失败: Seq={seqnum}");
                    }

                    // 定期输出统计信息
                    if (seqnum % 100 == 0)
                    {
                        Debug.Log($"[AudioExtrasSink] 音频处理统计: Seq={seqnum}, 解码={decodedLength}样本, 队列={_currentQueueSize}, 重采样={_needsResampling}");
                    }

                    _stats.FramesDecoded++;
                }
                else
                {
                    Debug.LogError($"[AudioExtrasSink] G.722解码失败: Seq={seqnum}, PayloadSize={payload.Length}, 返回长度={decodedLength}");
                    _stats.DecodeErrors++;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] 音频解码异常: Seq={seqnum}, Error={ex.Message}");
                _stats.DecodeErrors++;
            }
        }

        /// <summary>
        /// RTP解包后调用，输入G.722音频帧（简化接口）
        /// </summary>
        public void GotAudioRtp(byte[] g722Data)
        {
            GotAudioRtp(null, 0, _lastSequenceNumber + 1, 0, 9, false, g722Data);
        }

        /// <summary>
        /// Unity音频 回调 - 改进版本，处理数据大小不匹配问题
        /// /// </summary>
        private void OnAudioRead(float[] data)
        {
            _stats.AudioReadCalls++;

            // 🔥 从环形缓冲区读取音频数据 - 高性能版本
            int dataFilled = ReadFromRingBuffer(data);

            // 更新队列大小统计（用于兼容性）
            _currentQueueSize = GetUsedSpace() / _frameSize;

            // 🔥 强制调试：每次调用都输出日志（临时诊断）
            Debug.Log($"[OnAudioRead-V4.0] 🔥 调用#{_stats.AudioReadCalls}, 请求={data.Length}, 填充={dataFilled}, 缓冲区帧数={_currentQueueSize}, 时间={Time.realtimeSinceStartup:F2}s");

            // 检查AudioSource状态
            if (_audioSource != null && _stats.AudioReadCalls <= 5)
            {
                Debug.Log($"[OnAudioRead-V4.0] AudioSource状态: 播放={_audioSource.isPlaying}, 音量={_audioSource.volume}, 静音={_audioSource.mute}");
            }

            // 🔥 统计音频帧和静音帧 - V3.0修复统计逻辑
            if (dataFilled > 0)
            {
                // 有音频数据就算作音频帧 - 这是关键修复！
                _stats.AudioFrames++;
                Debug.Log($"[OnAudioRead-V3.0] 🎉 音频帧#{_stats.AudioFrames}: 填充了{dataFilled}/{data.Length}样本");

                // 如果还有空间未填充，用静音填充剩余部分
                if (dataFilled < data.Length)
                {
                    Array.Clear(data, dataFilled, data.Length - dataFilled);
                    // 不增加静音帧计数，因为这次调用主要是音频数据
                }
            }
            else
            {
                // 完全没有音频数据，填充静音
                Array.Clear(data, 0, data.Length);
                _stats.SilentFrames++;
                Debug.Log($"[OnAudioRead-V3.0] ⭕ 静音帧#{_stats.SilentFrames}: 队列为空，填充{data.Length}样本静音");
            }

            // 更新播放状态
            if (dataFilled > 0)
            {
                _lastAudioPlayTime = Time.realtimeSinceStartup;
                _hasPlayedAudio = true;
            }

            // 保存播放音频数据用于调试
            SavePlaybackAudio(data);
        }

        private void OnAudioSetPosition(int newPosition)
        {
            // 不需要实现
        }


        // IAudioSink接口要求
        public void RestrictFormats(Func<AudioFormat, bool> filter) { }
        public void SetAudioSinkFormat(AudioFormat format) { }
        public List<AudioFormat> GetAudioSinkFormats() => new List<AudioFormat> { new AudioFormat(SDPWellKnownMediaFormatsEnum.G722) };
        public System.Threading.Tasks.Task PauseAudioSink() { _audioSource?.Pause(); return System.Threading.Tasks.Task.CompletedTask; }
        public System.Threading.Tasks.Task ResumeAudioSink() { _audioSource?.UnPause(); return System.Threading.Tasks.Task.CompletedTask; }
        public System.Threading.Tasks.Task StartAudioSink() { Start(); return System.Threading.Tasks.Task.CompletedTask; }
        public System.Threading.Tasks.Task CloseAudioSink() { Close(); return System.Threading.Tasks.Task.CompletedTask; }

        /// <summary>
        /// 初始化音频保存功能
        /// </summary>
        private void InitializeAudioSaving()
        {
            if (!_enableAudioSaving || _audioSavingInitialized) return;

            if (_unityPlaybackSampleRate <= 0)
            {
                Debug.LogWarning("[AudioExtrasSink] 采样率未初始化，跳过音频保存初始化");
                return;
            }

            try
            {
                _audioSavePath = Path.Combine(Application.persistentDataPath, "AudioDebug");
                if (!Directory.Exists(_audioSavePath))
                {
                    Directory.CreateDirectory(_audioSavePath);
                }

                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                string decodedFileName = $"decoded_audio_{timestamp}.wav";
                string playbackFileName = $"playback_audio_{timestamp}.wav";

                int fileIndex = 1;
                while (File.Exists(Path.Combine(_audioSavePath, decodedFileName)))
                {
                    decodedFileName = $"decoded_audio_{timestamp}_{fileIndex:D3}.wav";
                    playbackFileName = $"playback_audio_{timestamp}_{fileIndex:D3}.wav";
                    fileIndex++;
                }

                string decodedFilePath = Path.Combine(_audioSavePath, decodedFileName);
                try
                {
                    _decodedAudioFile = new FileStream(decodedFilePath, FileMode.Create, FileAccess.Write, FileShare.None);
                    _decodedWriter = new BinaryWriter(_decodedAudioFile);
                }
                catch (IOException ex)
                {
                    Debug.LogWarning($"[AudioExtrasSink] 创建解码音频文件失败，尝试使用备用名称: {ex.Message}");
                    string backupDecodedFileName = $"decoded_audio_{DateTime.Now.Ticks}.wav";
                    string backupDecodedFilePath = Path.Combine(_audioSavePath, backupDecodedFileName);
                    _decodedAudioFile = new FileStream(backupDecodedFilePath, FileMode.Create, FileAccess.Write, FileShare.None);
                    _decodedWriter = new BinaryWriter(_decodedAudioFile);
                    decodedFilePath = backupDecodedFilePath;
                }

                string playbackFilePath = Path.Combine(_audioSavePath, playbackFileName);
                try
                {
                    _playbackAudioFile = new FileStream(playbackFilePath, FileMode.Create, FileAccess.Write, FileShare.None);
                    _playbackWriter = new BinaryWriter(_playbackAudioFile);
                }
                catch (IOException ex)
                {
                    Debug.LogWarning($"[AudioExtrasSink] 创建播放音频文件失败，尝试使用备用名称: {ex.Message}");
                    string backupPlaybackFileName = $"playback_audio_{DateTime.Now.Ticks}.wav";
                    string backupPlaybackFilePath = Path.Combine(_audioSavePath, backupPlaybackFileName);
                    _playbackAudioFile = new FileStream(backupPlaybackFilePath, FileMode.Create, FileAccess.Write, FileShare.None);
                    _playbackWriter = new BinaryWriter(_playbackAudioFile);
                    playbackFilePath = backupPlaybackFilePath;
                }

                // 修正：解码后的WAV文件头必须使用16000Hz采样率
                WriteWavHeader(_decodedWriter, G722_PCM_SAMPLE_RATE, 1, 16);
                WriteWavHeader(_playbackWriter, _unityPlaybackSampleRate, 1, 16);

                _savedDecodedSamples = 0;
                _savedPlaybackSamples = 0;
                _audioSavingInitialized = true;

                Debug.Log($"[AudioExtrasSink] 音频保存功能已初始化:");
                Debug.Log($"[AudioExtrasSink] - 解码音频: {decodedFilePath}");
                Debug.Log($"[AudioExtrasSink] - 播放音频: {playbackFilePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] 初始化音频保存失败: {ex.Message}");
                _enableAudioSaving = false;
                _audioSavingInitialized = false;
                
                try
                {
                    _decodedWriter?.Close();
                    _decodedWriter = null;
                    _decodedAudioFile?.Close();
                    _decodedAudioFile = null;
                    _playbackWriter?.Close();
                    _playbackWriter = null;
                    _playbackAudioFile?.Close();
                    _playbackAudioFile = null;
                }
                catch { }
            }
        }

        /// <summary>
        /// 写入WAV文件头
        /// </summary>
        private void WriteWavHeader(BinaryWriter writer, int sampleRate, int channels, int bitsPerSample)
        {
            writer.Write(System.Text.Encoding.ASCII.GetBytes("RIFF"));
            writer.Write(0);
            writer.Write(System.Text.Encoding.ASCII.GetBytes("WAVE"));

            writer.Write(System.Text.Encoding.ASCII.GetBytes("fmt "));
            writer.Write(16);
            writer.Write((short)1);
            writer.Write((short)channels);
            writer.Write(sampleRate);
            writer.Write(sampleRate * channels * bitsPerSample / 8);
            writer.Write((short)(channels * bitsPerSample / 8));
            writer.Write((short)bitsPerSample);

            writer.Write(System.Text.Encoding.ASCII.GetBytes("data"));
            writer.Write(0);
        }

        /// <summary>
        /// 保存解码后的音频数据
        /// </summary>
        private void SaveDecodedAudio(short[] pcmData)
        {
            if (!_enableAudioSaving || _decodedWriter == null || _savedDecodedSamples >= MAX_SAVE_SAMPLES) return;

            try
            {
                for (int i = 0; i < pcmData.Length; i++)
                {
                    _decodedWriter.Write(pcmData[i]);
                }
                _savedDecodedSamples += pcmData.Length;

                if (_savedDecodedSamples % G722_PCM_SAMPLE_RATE == 0)
                {
                    float savedSeconds = (float)_savedDecodedSamples / G722_PCM_SAMPLE_RATE;
                    Debug.Log($"[AudioExtrasSink] 已保存解码音频: {savedSeconds:F1}秒 ({_savedDecodedSamples}样本)");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] 保存解码音频失败: {ex.Message}");
                _enableAudioSaving = false;
            }
        }

        /// <summary>
        /// 保存播放音频数据
        /// </summary>
        private void SavePlaybackAudio(float[] audioData)
        {
            if (!_enableAudioSaving || _playbackWriter == null || _savedPlaybackSamples >= MAX_SAVE_SAMPLES) return;

            try
            {
                for (int i = 0; i < audioData.Length; i++)
                {
                    short pcmSample = (short)(audioData[i] * 32767f);
                    _playbackWriter.Write(pcmSample);
                }
                _savedPlaybackSamples += audioData.Length;

                if (_savedPlaybackSamples % _unityPlaybackSampleRate == 0)
                {
                    float savedSeconds = (float)_savedPlaybackSamples / _unityPlaybackSampleRate;
                    Debug.Log($"[AudioExtrasSink] 已保存播放音频: {savedSeconds:F1}秒 ({_savedPlaybackSamples}样本)");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] 保存播放音频失败: {ex.Message}");
                _enableAudioSaving = false;
            }
        }

        /// <summary>
        /// 关闭音频保存功能
        /// </summary>
        private void CloseAudioSaving()
        {
            if (!_enableAudioSaving || !_audioSavingInitialized) return;

            try
            {
                if (_decodedWriter != null)
                {
                    UpdateWavFileSize(_decodedAudioFile, _savedDecodedSamples * 2);
                    _decodedWriter.Close();
                    _decodedWriter = null;
                }

                if (_playbackWriter != null)
                {
                    UpdateWavFileSize(_playbackAudioFile, _savedPlaybackSamples * 2);
                    _playbackWriter.Close();
                    _playbackWriter = null;
                }

                if (_decodedAudioFile != null)
                {
                    _decodedAudioFile.Close();
                    _decodedAudioFile = null;
                }

                if (_playbackAudioFile != null)
                {
                    _playbackAudioFile.Close();
                    _playbackAudioFile = null;
                }

                Debug.Log($"[AudioExtrasSink] 音频保存完成:");
                Debug.Log($"[AudioExtrasSink] - 解码音频: {_savedDecodedSamples}样本 ({_savedDecodedSamples / (float)G722_PCM_SAMPLE_RATE:F2}秒)");
                Debug.Log($"[AudioExtrasSink] - 播放音频: {_savedPlaybackSamples}样本 ({_savedPlaybackSamples / (float)_unityPlaybackSampleRate:F2}秒)");
                Debug.Log($"[AudioExtrasSink] - 保存路径: {_audioSavePath}");

                _audioSavingInitialized = false;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] 关闭音频保存失败: {ex.Message}");
                _audioSavingInitialized = false;
            }
        }

        /// <summary>
        /// 更新WAV文件大小信息
        /// </summary>
        private void UpdateWavFileSize(FileStream fileStream, int dataSize)
        {
            if (fileStream == null) return;

            try
            {
                fileStream.Seek(4, SeekOrigin.Begin);
                fileStream.Write(BitConverter.GetBytes(36 + dataSize), 0, 4);

                fileStream.Seek(40, SeekOrigin.Begin);
                fileStream.Write(BitConverter.GetBytes(dataSize), 0, 4);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AudioExtrasSink] 更新WAV文件大小失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启用或禁用音频保存功能
        /// </summary>
        public void SetAudioSavingEnabled(bool enable)
        {
            if (_enableAudioSaving == enable) return;

            _enableAudioSaving = enable;
            
            if (!enable && _audioSavingInitialized)
            {
                CloseAudioSaving();
            }
            
            Debug.Log($"[AudioExtrasSink] 音频保存功能已{(enable ? "启用" : "禁用")}");
        }

        /// <summary>
        /// 获取音频保存状态
        /// </summary>
        public bool IsAudioSavingEnabled()
        {
            return _enableAudioSaving;
        }

        /// <summary>
        /// 获取音频保存路径
        /// </summary>
        public string GetAudioSavePath()
        {
            return _audioSavePath;
        }

        /// <summary>
        /// 验证解码后的音频数据质量
        /// </summary>
        private void ValidateAudioData(float[] audioData, uint sequenceNumber)
        {
            if (audioData == null || audioData.Length == 0)
            {
                Debug.LogError($"[AudioExtrasSink] 音频数据为空: Seq={sequenceNumber}");
                return;
            }

            float maxLevel = 0f;
            float avgLevel = 0f;
            int clippedSamples = 0;

            for (int i = 0; i < audioData.Length; i++)
            {
                float abs = Math.Abs(audioData[i]);
                maxLevel = Math.Max(maxLevel, abs);
                avgLevel += abs;

                if (abs > 1.0f)
                {
                    clippedSamples++;
                }
            }

            avgLevel /= audioData.Length;

            // 记录音频质量统计
            _stats.MaxAudioLevel = Math.Max(_stats.MaxAudioLevel, maxLevel);
            _stats.AvgAudioLevel = (_stats.AvgAudioLevel + avgLevel) / 2.0f;

            // 检查异常情况
            if (clippedSamples > 0)
            {
                Debug.LogError($"[AudioExtrasSink] 音频削波检测: Seq={sequenceNumber}, 削波样本={clippedSamples}/{audioData.Length}, 最大电平={maxLevel:F3}");
            }

            if (avgLevel < 0.001f && sequenceNumber % 50 == 0) // 减少低电平警告频率
            {
                Debug.LogWarning($"[AudioExtrasSink] 音频电平过低: Seq={sequenceNumber}, 平均电平={avgLevel:F6}");
            }

            if (maxLevel > 0.95f)
            {
                Debug.LogWarning($"[AudioExtrasSink] 音频电平过高: Seq={sequenceNumber}, 最大电平={maxLevel:F3}");
            }
        }

        /// <summary>
        /// 简单线性重采样实现
        /// </summary>
        private float[] ResampleAudio(float[] input, int inputSampleRate, int outputSampleRate)
        {
            if (inputSampleRate == outputSampleRate)
                return input;

            double ratio = (double)inputSampleRate / outputSampleRate;
            int outputLength = (int)(input.Length / ratio);
            float[] output = new float[outputLength];

            for (int i = 0; i < outputLength; i++)
            {
                double sourceIndex = i * ratio;
                int index1 = (int)sourceIndex;
                int index2 = Math.Min(index1 + 1, input.Length - 1);
                double fraction = sourceIndex - index1;

                // 线性插值
                if (index1 < input.Length)
                {
                    output[i] = (float)(input[index1] * (1.0 - fraction) + input[index2] * fraction);
                }
            }

            return output;
        }

        /// <summary>
        /// 强制重启AudioSource播放
        /// </summary>
        public void ForceRestartAudioSource()
        {
            if (_audioSource != null)
            {
                Debug.Log("[AudioExtrasSink] 🔥 强制重启AudioSource...");

                // 停止播放
                _audioSource.Stop();
                Debug.Log($"[AudioExtrasSink] AudioSource已停止");

                // 等待一帧后重新开始
                StartCoroutine(RestartAudioSourceDelayed());
            }
        }

        /// <summary>
        /// 延迟重启AudioSource
        /// </summary>
        private System.Collections.IEnumerator RestartAudioSourceDelayed()
        {
            yield return null; // 等待一帧

            if (_audioSource != null && _audioSource.clip != null)
            {
                _audioSource.Play();
                Debug.Log($"[AudioExtrasSink] AudioSource已重启，播放状态: {_audioSource.isPlaying}");

                // 重置统计
                _stats.AudioReadCalls = 0;
                _stats.AudioFrames = 0;
                _stats.SilentFrames = 0;

                Debug.Log("[AudioExtrasSink] 统计信息已重置");
            }
        }

        /// <summary>
        /// 检查音频环境配置 - 调试用
        /// </summary>
        public void CheckAudioEnvironment()
        {
            Debug.Log("=== Unity音频环境检查 ===");

            // Unity音频设置
            var config = AudioSettings.GetConfiguration();
            Debug.Log($"Unity音频配置:");
            Debug.Log($"  - 输出采样率: {AudioSettings.outputSampleRate}Hz");
            Debug.Log($"  - DSP缓冲区: {config.dspBufferSize}样本");
            Debug.Log($"  - 扬声器模式: {config.speakerMode}");

            // AudioSource状态
            if (_audioSource != null)
            {
                Debug.Log($"AudioSource状态:");
                Debug.Log($"  - 是否播放: {_audioSource.isPlaying}");
                Debug.Log($"  - 音量: {_audioSource.volume}");
                Debug.Log($"  - 优先级: {_audioSource.priority}");

                if (_audioSource.clip != null)
                {
                    Debug.Log($"  - Clip采样率: {_audioSource.clip.frequency}Hz");
                    Debug.Log($"  - Clip长度: {_audioSource.clip.length:F2}秒");
                    Debug.Log($"  - Clip声道: {_audioSource.clip.channels}");
                }
                else
                {
                    Debug.LogError("  - AudioClip未设置！");
                }
            }
            else
            {
                Debug.LogError("AudioSource未设置！");
            }

            // G.722解码器状态
            Debug.Log($"G.722解码器:");
            Debug.Log($"  - 解码器已初始化: {_g722Codec != null && _g722Decoder != null}");
            Debug.Log($"  - 输入采样率: {G722_RTP_CLOCK_RATE}Hz (RTP时钟)");
            Debug.Log($"  - 输出采样率: {G722_PCM_SAMPLE_RATE}Hz (PCM)");
            Debug.Log($"  - 播放采样率: {_unityPlaybackSampleRate}Hz");
            Debug.Log($"  - 需要重采样: {_needsResampling}");

            // 统计信息
            Debug.Log($"音频统计:");
            Debug.Log($"  - 接收包数: {_stats.PacketsReceived}");
            Debug.Log($"  - 解码帧数: {_stats.FramesDecoded}");
            Debug.Log($"  - 播放帧数: {_stats.AudioFrames}");
            Debug.Log($"  - 静音帧数: {_stats.SilentFrames}");
            Debug.Log($"  - 队列大小: {_currentQueueSize}");
            Debug.Log($"  - 最大音频电平: {_stats.MaxAudioLevel:F3}");
            Debug.Log($"  - 平均音频电平: {_stats.AvgAudioLevel:F3}");

            Debug.Log("=== 检查完成 ===");
        }

        /// <summary>
        /// 音频播放状态监控协程
        /// </summary>
        private System.Collections.IEnumerator AudioStatusMonitor()
        {
            while (_isStarted)
            {
                yield return new WaitForSeconds(5.0f); // 每5秒检查一次

                // 检查音频播放状态
                float timeSinceLastAudio = Time.realtimeSinceStartup - _lastAudioPlayTime;
                bool audioSourcePlaying = _audioSource != null && _audioSource.isPlaying;

                Debug.Log($"[AudioExtrasSink] 音频状态监控:");
                Debug.Log($"  - AudioSource播放: {audioSourcePlaying}");
                Debug.Log($"  - 距离上次音频: {timeSinceLastAudio:F1}秒");
                Debug.Log($"  - 当前队列大小: {_currentQueueSize}");
                Debug.Log($"  - 已播放音频: {_hasPlayedAudio}");
                Debug.Log($"  - 统计信息: 接收={_stats.PacketsReceived}, 解码={_stats.FramesDecoded}, 播放={_stats.AudioFrames}, 静音={_stats.SilentFrames}");

                // 检查异常情况
                if (!audioSourcePlaying && _isStarted)
                {
                    Debug.LogError("[AudioExtrasSink] AudioSource未在播放，尝试重新启动");
                    if (_audioSource != null)
                    {
                        _audioSource.Play();
                    }
                }

                if (timeSinceLastAudio > 10.0f && _stats.PacketsReceived > 0)
                {
                    Debug.LogError($"[AudioExtrasSink] 长时间无音频播放 ({timeSinceLastAudio:F1}秒)，可能存在问题");
                }

                if (_currentQueueSize == 0 && _stats.PacketsReceived > 100)
                {
                    Debug.LogWarning("[AudioExtrasSink] 音频队列为空，但已接收大量包，可能存在处理问题");
                }
            }
        }

        /// <summary>
        /// 生成测试音频信号 - 调试用
        /// </summary>
        public void GenerateTestAudio()
        {
            Debug.Log("[AudioExtrasSink] 生成测试音频信号...");

            // 生成1kHz正弦波测试信号
            int sampleCount = G722_PCM_SAMPLE_RATE / 50; // 20ms的样本数
            float[] testAudio = new float[sampleCount];

            for (int i = 0; i < sampleCount; i++)
            {
                float time = (float)i / G722_PCM_SAMPLE_RATE;
                testAudio[i] = 0.3f * Mathf.Sin(2 * Mathf.PI * 1000 * time); // 1kHz, 30%音量
            }

            // 如果需要重采样
            if (_needsResampling)
            {
                testAudio = ResampleAudio(testAudio, G722_PCM_SAMPLE_RATE, _unityPlaybackSampleRate);
            }

            // 🔥 直接写入环形缓冲区
            bool writeSuccess = WriteToRingBuffer(testAudio);

            if (writeSuccess)
            {
                Debug.Log($"[AudioExtrasSink] 🔥 测试音频已写入环形缓冲区，样本数: {testAudio.Length}");
            }
            else
            {
                Debug.LogError($"[AudioExtrasSink] 测试音频写入环形缓冲区失败");
            }
        }
    }
}
