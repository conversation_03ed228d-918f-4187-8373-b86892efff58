# Unity SIP 音频播放问题诊断报告

## 📋 问题概述

**问题描述**：当前Unity SIP系统无法听到解码后的音频，需要系统性排查音频播放链路中的技术问题。

**检查日期**：2025年7月15日  
**检查范围**：AudioExtrasSink.cs音频解码流程、Unity AudioClip播放系统、音频数据链路完整性、系统配置环境

---

## 🔍 详细问题分析

### 1. AudioExtrasSink.cs音频解码流程问题

#### 1.1 G.722解码器配置问题
**发现的问题**：
```csharp
// 当前代码中的问题
private const int G722_PCM_SAMPLE_RATE = 16000; // G.722 PCM采样率 (解码后)
_unityPlaybackSampleRate = G722_PCM_SAMPLE_RATE; // 设置为16kHz

// 但AudioClip创建时使用了16kHz
_audioSource.clip = AudioClip.Create("SIPAudioPlayback", clipLengthSamples, CHANNELS, _unityPlaybackSampleRate, true, OnAudioRead, OnAudioSetPosition);
```

**问题分析**：
- G.722解码后确实产生16kHz PCM数据
- 但Unity系统默认采样率通常是44.1kHz或48kHz
- 直接使用16kHz播放可能导致音频播放异常

#### 1.2 RTP包接收和解码逻辑检查
**代码检查结果**：
```csharp
public void GotAudioRtp(System.Net.IPEndPoint remoteEndPoint, uint ssrc, uint seqnum, uint timestamp, int payloadType, bool marker, byte[] payload)
{
    // ✅ 基本验证正常
    if (!_isStarted || payload == null || payload.Length == 0) return;
    
    // ✅ 丢包检测逻辑正常
    // ✅ G.722解码逻辑正常
    var decodedSamples = new short[payload.Length * 2];
    int decodedLength = _g722Codec.Decode(_g722Decoder, decodedSamples, payload, payload.Length);
    
    // ⚠️ 潜在问题：数据格式转换
    for (int i = 0; i < decodedLength; i++)
    {
        floatData[i] = actualDecoded[i] / 32768.0f; // 可能存在精度问题
    }
}
```

#### 1.3 PCM数据格式转换问题
**发现的问题**：
- 使用`/ 32768.0f`进行转换，但应该使用`/ 32767.0f`（16位有符号整数的最大值）
- 没有进行范围检查，可能导致音频削波

### 2. Unity AudioClip播放系统问题

#### 2.1 OnAudioRead()回调函数问题
**关键问题发现**：
```csharp
private void OnAudioRead(float[] data)
{
    lock (_queueLock)
    {
        if (_audioQueue.Count > 0)
        {
            float[] audioFrame = _audioQueue.Dequeue();
            
            // ⚠️ 问题1：数据长度不匹配
            int samplesToCopy = Math.Min(audioFrame.Length, data.Length);
            Array.Copy(audioFrame, 0, data, 0, samplesToCopy);
            
            // ⚠️ 问题2：G.722帧大小(320样本)与Unity请求大小可能不匹配
        }
        else
        {
            // ⚠️ 问题3：队列为空时输出静音，可能导致音频中断
            Array.Clear(data, 0, data.Length);
        }
    }
}
```

#### 2.2 Unity AudioSource配置问题
**配置检查结果**：
```csharp
// AudioExtrasSink.DelayedInitialization()中的配置
_audioSource.playOnAwake = false;     // ✅ 正确
_audioSource.loop = true;             // ✅ 正确
_audioSource.spatialBlend = 0f;       // ✅ 正确（2D音频）
_audioSource.volume = _volume;        // ✅ 正确
_audioSource.Play();                  // ✅ 已调用播放

// ⚠️ 潜在问题：采样率不匹配
// Unity系统采样率 vs AudioClip采样率 vs G.722解码采样率
```

### 3. 音频数据链路完整性问题

#### 3.1 RTP包接收验证
**需要验证的关键点**：
- RTP包是否正常到达`HandleRtpPacketReceived`方法
- Payload Type是否正确（应该是9，对应G.722）
- 音频数据是否成功传递到`AudioExtrasSink.GotAudioRtp`

#### 3.2 音频缓冲区管理问题
**发现的问题**：
```csharp
private const int MAX_QUEUE_SIZE = 50; // 最大队列大小（1秒音频）

// ⚠️ 问题：队列管理可能导致音频延迟或丢失
while (_audioQueue.Count > MAX_QUEUE_SIZE)
{
    _audioQueue.Dequeue(); // 直接丢弃旧数据，可能导致音频不连续
}
```

### 4. 系统配置和环境因素

#### 4.1 Unity音频设置问题
**检查发现**：
```csharp
// 从文档中发现的历史问题
if (AudioSettings.outputSampleRate == 8000)
{
    // 之前的修复代码，但可能仍有问题
    var resetConfig = AudioSettings.GetConfiguration();
    resetConfig.sampleRate = 0; // 使用系统默认采样率
    AudioSettings.Reset(resetConfig);
}
```

#### 4.2 音频设备和权限问题
**需要检查的项目**：
- 系统音频设备是否正常工作
- Unity是否有音频播放权限
- 音频驱动程序是否兼容

---

## 🎯 根本原因分析

### 主要问题
1. **采样率不匹配**：G.722解码输出16kHz，但Unity系统可能使用不同采样率
2. **音频数据格式转换错误**：PCM到float转换使用了错误的除数
3. **音频缓冲区大小不匹配**：G.722帧大小与Unity AudioRead请求大小不匹配
4. **音频队列管理不当**：可能导致音频数据丢失或不连续

### 次要问题
1. **缺乏音频播放状态监控**：无法确定AudioSource是否真正在播放
2. **错误处理不完善**：解码失败时缺乏有效的错误恢复机制
3. **调试信息不足**：难以追踪音频数据流的完整路径

---

## 🔧 具体修复建议

### 1. 修复采样率不匹配问题
```csharp
// 在AudioExtrasSink.DelayedInitialization()中添加
private System.Collections.IEnumerator DelayedInitialization()
{
    yield return null;
    
    // 获取Unity系统采样率
    int systemSampleRate = AudioSettings.outputSampleRate;
    Debug.Log($"[AudioExtrasSink] Unity系统采样率: {systemSampleRate}Hz");
    
    // 使用系统采样率而不是固定的16kHz
    _unityPlaybackSampleRate = systemSampleRate;
    
    // 如果需要重采样，实现重采样逻辑
    if (systemSampleRate != G722_PCM_SAMPLE_RATE)
    {
        Debug.LogWarning($"[AudioExtrasSink] 需要从{G722_PCM_SAMPLE_RATE}Hz重采样到{systemSampleRate}Hz");
        // 实现重采样逻辑
    }
}
```

### 2. 修复PCM数据格式转换
```csharp
// 修复GotAudioRtp方法中的转换逻辑
for (int i = 0; i < decodedLength; i++)
{
    // 修复：使用正确的除数和范围检查
    float sample = actualDecoded[i] / 32767.0f; // 16位有符号整数最大值
    floatData[i] = Mathf.Clamp(sample, -1.0f, 1.0f); // 确保范围正确
}
```

### 3. 改进OnAudioRead回调处理
```csharp
private void OnAudioRead(float[] data)
{
    lock (_queueLock)
    {
        int dataIndex = 0;
        
        // 尽可能填充Unity请求的数据
        while (dataIndex < data.Length && _audioQueue.Count > 0)
        {
            float[] audioFrame = _audioQueue.Dequeue();
            int remainingSpace = data.Length - dataIndex;
            int samplesToCopy = Math.Min(audioFrame.Length, remainingSpace);
            
            Array.Copy(audioFrame, 0, data, dataIndex, samplesToCopy);
            dataIndex += samplesToCopy;
            
            // 如果帧没有完全使用，需要处理剩余部分
            if (samplesToCopy < audioFrame.Length)
            {
                // 创建新帧保存剩余数据
                float[] remainingFrame = new float[audioFrame.Length - samplesToCopy];
                Array.Copy(audioFrame, samplesToCopy, remainingFrame, 0, remainingFrame.Length);
                
                // 将剩余数据放回队列前端（需要实现优先队列）
                // 或者使用其他策略处理
            }
        }
        
        // 如果还有空间未填充，用静音填充
        if (dataIndex < data.Length)
        {
            Array.Clear(data, dataIndex, data.Length - dataIndex);
        }
    }
    
    // 添加音频播放监控
    if (dataIndex > 0)
    {
        _lastAudioPlayTime = Time.realtimeSinceStartup;
    }
}
```

### 4. 添加音频播放状态监控
```csharp
// 在AudioExtrasSink中添加监控字段
private float _lastAudioPlayTime = 0f;
private int _totalFramesPlayed = 0;
private int _emptyFrameCount = 0;

// 添加状态检查方法
public void CheckAudioPlaybackStatus()
{
    float timeSinceLastAudio = Time.realtimeSinceStartup - _lastAudioPlayTime;
    
    if (timeSinceLastAudio > 1.0f) // 1秒没有音频数据
    {
        Debug.LogWarning($"[AudioExtrasSink] 音频播放可能异常 - {timeSinceLastAudio:F2}秒没有音频数据");
    }
    
    Debug.Log($"[AudioExtrasSink] 播放状态 - 总帧数:{_totalFramesPlayed}, 空帧数:{_emptyFrameCount}, 队列大小:{_audioQueue.Count}");
}
```

### 5. 实现重采样功能
```csharp
// 添加简单的线性重采样实现
private float[] ResampleAudio(float[] input, int inputSampleRate, int outputSampleRate)
{
    if (inputSampleRate == outputSampleRate)
        return input;
    
    float ratio = (float)inputSampleRate / outputSampleRate;
    int outputLength = (int)(input.Length / ratio);
    float[] output = new float[outputLength];
    
    for (int i = 0; i < outputLength; i++)
    {
        float sourceIndex = i * ratio;
        int index1 = (int)sourceIndex;
        int index2 = Math.Min(index1 + 1, input.Length - 1);
        float fraction = sourceIndex - index1;
        
        // 线性插值
        output[i] = input[index1] * (1 - fraction) + input[index2] * fraction;
    }
    
    return output;
}
```

---

## 🧪 调试方法和验证步骤

### 1. 音频数据流追踪
```csharp
// 在关键位置添加调试日志
Debug.Log($"[AudioExtrasSink] RTP包接收: Seq={seqnum}, PayloadSize={payload.Length}, 解码样本={decodedLength}");
Debug.Log($"[AudioExtrasSink] 队列状态: Count={_audioQueue.Count}, Unity请求={data.Length}样本");
Debug.Log($"[AudioExtrasSink] AudioSource状态: isPlaying={_audioSource.isPlaying}, volume={_audioSource.volume}");
```

### 2. 音频数据验证
```csharp
// 验证解码后的音频数据
private void ValidateAudioData(float[] audioData)
{
    float maxLevel = 0f;
    float avgLevel = 0f;
    
    for (int i = 0; i < audioData.Length; i++)
    {
        float abs = Math.Abs(audioData[i]);
        maxLevel = Math.Max(maxLevel, abs);
        avgLevel += abs;
    }
    
    avgLevel /= audioData.Length;
    
    if (maxLevel > 1.0f)
    {
        Debug.LogError($"[AudioExtrasSink] 音频数据超出范围: max={maxLevel}");
    }
    
    if (avgLevel < 0.001f)
    {
        Debug.LogWarning($"[AudioExtrasSink] 音频数据可能过小: avg={avgLevel}");
    }
}
```

### 3. 系统音频环境检查
```csharp
// 添加系统音频环境检查
private void CheckAudioEnvironment()
{
    Debug.Log($"[AudioExtrasSink] Unity音频配置:");
    Debug.Log($"  - 输出采样率: {AudioSettings.outputSampleRate}Hz");
    Debug.Log($"  - DSP缓冲区大小: {AudioSettings.GetConfiguration().dspBufferSize}");
    Debug.Log($"  - 扬声器模式: {AudioSettings.GetConfiguration().speakerMode}");
    
    Debug.Log($"[AudioExtrasSink] AudioSource配置:");
    Debug.Log($"  - 是否播放: {_audioSource.isPlaying}");
    Debug.Log($"  - 音量: {_audioSource.volume}");
    Debug.Log($"  - 优先级: {_audioSource.priority}");
    Debug.Log($"  - AudioClip: {(_audioSource.clip != null ? "已设置" : "未设置")}");
    
    if (_audioSource.clip != null)
    {
        Debug.Log($"  - Clip采样率: {_audioSource.clip.frequency}Hz");
        Debug.Log($"  - Clip长度: {_audioSource.clip.length}秒");
        Debug.Log($"  - Clip声道: {_audioSource.clip.channels}");
    }
}
```

---

## 📋 验证清单

### 立即检查项目
- [ ] 验证RTP包是否正常接收到AudioExtrasSink
- [ ] 检查G.722解码器是否成功解码音频数据
- [ ] 确认Unity AudioSource组件是否正在播放
- [ ] 验证音频队列是否有数据
- [ ] 检查系统音频设备是否正常工作

### 配置验证项目
- [ ] Unity系统采样率与AudioClip采样率是否匹配
- [ ] AudioSource音量设置是否正确
- [ ] 音频权限是否已获取
- [ ] 音频设备驱动是否正常

### 数据流验证项目
- [ ] RTP包payload数据是否有效
- [ ] G.722解码输出数据范围是否正确
- [ ] PCM到float转换是否准确
- [ ] OnAudioRead回调是否被正常调用

---

*报告生成时间：2025年7月15日*  
*建议优先级：高*  
*预计修复时间：2-4小时*
