Search Paths:78
D:\Unity\2020.3.48f1c1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api
D:\Unity\2020.3.48f1c1\Editor\Data\MonoBleedingEdge\lib\mono\4.7.1-api\Facades
D:\Unity\2020.3.48f1c1\Editor\Data\Managed
D:\Unity\2020.3.48f1c1\Editor\Data\Managed/UnityEngine
D:/Unity/司导/SIP2.0/Assets
D:/Unity/司导/SIP2.0/Assets\../Library/ScriptAssemblies/
Assets\Packages\System.Buffers.4.5.1\lib\netstandard2.0
Assets\Packages\SIPSorcery.8.0.14\lib\netstandard2.0
Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\netstandard2.0
Assets\Packages\SIPSorceryMedia.Abstractions.8.0.10\lib\netstandard2.0
Assets\Packages\System.Security.Principal.Windows.5.0.0\lib\netstandard2.0
Assets\Packages\DirectShowLib.Standard.2.1.0\lib\netstandard2.0
Assets\Packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\netstandard2.0
Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\netstandard2.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Lib\Editor\PlasticSCM
Assets\Packages\Portable.BouncyCastle.1.9.0\lib\netstandard2.0
Assets\Packages\System.Memory.4.5.5\lib\netstandard2.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.nuget.newtonsoft-json@3.0.2\Runtime
Assets\NuGet\Editor
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom
Assets\Packages\Concentus.2.2.2\lib\netstandard2.0
Assets\Packages\System.Security.AccessControl.5.0.0\lib\netstandard2.0
Assets\Packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\netstandard2.0
Assets\Packages\SIPSorcery.WebSocketSharp.0.0.1\lib\netstandard2.0
Assets\Packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\netstandard2.0
Assets\Packages\Microsoft.Win32.Registry.5.0.0\lib\netstandard2.0
Assets\Packages\System.Threading.Tasks.Extensions.4.5.4\lib\netstandard2.0
Assets\Packages\SIPSorceryMedia.FFmpeg.8.0.10\lib\netstandard2.0
Assets\Packages\FFmpeg.AutoGen.7.0.0\lib\netstandard2.0
Assets\Packages\DnsClient.1.8.0\lib\netstandard2.0
C:\Users\<USER>\OneDrive\文档\Cline\MCP\unity-mcp\UnityMcpBridge
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.ide.rider@3.0.21
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.ide.visualstudio@2.0.23
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.ide.vscode@1.2.5
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.test-framework@1.1.33
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.textmeshpro@3.0.6
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.timeline@1.4.8
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.toolchain.win-x86_64-linux-x86_64@2.0.10
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.ugui@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.ai@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.androidjni@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.animation@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.assetbundle@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.audio@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.autostreaming@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.cloth@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.director@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.imageconversion@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.imgui@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.jsonserialize@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.particlesystem@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.physics@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.physics2d@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.screencapture@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.terrain@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.terrainphysics@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.tilemap@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.ui@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.uielements@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.umbra@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.unityanalytics@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.unitywebrequest@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.unitywebrequestassetbundle@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.unitywebrequestaudio@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.unitywebrequesttexture@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.unitywebrequestwww@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.vehicles@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.video@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.vr@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.wind@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.xr@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.subsystems@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.modules.uielementsnative@1.0.0
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.sysroot@2.0.10
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.sysroot.linux-x86_64@2.0.9
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.ext.nunit@1.0.6
D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.nuget.newtonsoft-json@3.0.2
D:\Unity\2020.3.48f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll:32
UnityEditor.Sysroot
UnityEditor.MPE.ChannelClient
UnityEditor.MPE.ChannelClientScope
UnityEditor.MPE.ChannelService
UnityEditor.MPE.ChannelScope
UnityEditor.MPE.EventDataSerialization
UnityEditor.MPE.EventService
UnityEditor.MPE.RoleProviderAttribute
UnityEditor.MPE.ProcessEvent
UnityEditor.MPE.ProcessLevel
UnityEditor.MPE.ProcessState
UnityEditor.MPE.RoleCapability
UnityEditor.MPE.ChannelInfo
UnityEditor.MPE.ChannelClientInfo
UnityEditor.MPE.ProcessService
UnityEditor.AI.NavMeshBuilder
UnityEditor.AI.NavMeshVisualizationSettings
UnityEditor.AssetImporters.CollectImportedDependenciesAttribute
UnityEditor.AssetImporters.AssetImportContext
UnityEditor.AssetImporters.SpriteImportData
UnityEditor.AssetImporters.TextureGenerationOutput
UnityEditor.AssetImporters.SourceTextureInformation
UnityEditor.AssetImporters.TextureGenerationSettings
UnityEditor.AssetImporters.TextureGenerator
UnityEditor.AssetImporters.FBXMaterialDescriptionPreprocessor
UnityEditor.AssetImporters.SketchupMaterialDescriptionPreprocessor
UnityEditor.AssetImporters.ThreeDSMaterialDescriptionPreprocessor
UnityEditor.AssetImporters.AssetImporterEditor
UnityEditor.AssetImporters.AssetImporterEditorPostProcessAsset
UnityEditor.AssetImporters.ScriptedImporterEditor
UnityEditor.AssetImporters.ScriptedImporter
UnityEditor.AssetImporters.ScriptedImporterAttribute
D:\Unity\2020.3.48f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll:7
UnityEngine.U2D.SpriteShapeParameters
UnityEngine.U2D.SpriteShapeSegment
UnityEngine.U2D.SpriteShapeRenderer
UnityEngine.U2D.SpriteShapeMetaData
UnityEngine.U2D.ShapeControlPoint
UnityEngine.U2D.AngleRangeInfo
UnityEngine.U2D.SpriteShapeUtility
D:\Unity\2020.3.48f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll:1
UnityEngine.LocalizationAsset
D:\Unity\2020.3.48f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll:25
UnityEngine.U2D.PixelPerfectRendering
UnityEngine.U2D.SpriteBone
UnityEngine.Profiling.Profiler
UnityEngine.Windows.WebCam.PhotoCaptureFileOutputFormat
UnityEngine.Windows.WebCam.PhotoCapture
UnityEngine.Windows.WebCam.PhotoCaptureFrame
UnityEngine.Windows.WebCam.VideoCapture
UnityEngine.Windows.WebCam.CapturePixelFormat
UnityEngine.Windows.WebCam.WebCamMode
UnityEngine.Windows.WebCam.WebCam
UnityEngine.Windows.WebCam.CameraParameters
UnityEngine.LowLevel.PlayerLoopSystemInternal
UnityEngine.LowLevel.PlayerLoopSystem
UnityEngine.LowLevel.PlayerLoop
UnityEngine.PlayerLoop.Initialization
UnityEngine.PlayerLoop.EarlyUpdate
UnityEngine.PlayerLoop.FixedUpdate
UnityEngine.PlayerLoop.PreUpdate
UnityEngine.PlayerLoop.Update
UnityEngine.PlayerLoop.PreLateUpdate
UnityEngine.PlayerLoop.PostLateUpdate
UnityEngine.Networking.PlayerConnection.ConnectionTarget
UnityEngine.Networking.PlayerConnection.IConnectionState
UnityEngine.Rendering.VertexAttribute
UnityEngine.Rendering.RenderingThreadingMode
D:\Unity\2020.3.48f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll:17
UnityEngine.AvatarMaskBodyPart
UnityEngine.AvatarMask
UnityEngine.Animations.IAnimationJob
UnityEngine.Animations.IAnimationJobPlayable
UnityEngine.Animations.IAnimationWindowPreview
UnityEngine.Animations.AnimationHumanStream
UnityEngine.Animations.AnimationScriptPlayable
UnityEngine.Animations.AnimationStream
UnityEngine.Animations.TransformStreamHandle
UnityEngine.Animations.PropertyStreamHandle
UnityEngine.Animations.TransformSceneHandle
UnityEngine.Animations.PropertySceneHandle
UnityEngine.Animations.AnimationSceneHandleUtility
UnityEngine.Animations.AnimationStreamHandleUtility
UnityEngine.Animations.CustomStreamPropertyType
UnityEngine.Animations.AnimatorJobExtensions
UnityEngine.Animations.MuscleHandle
D:\Unity\2020.3.48f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll:12
UnityEngine.AI.NavMeshPathStatus
UnityEngine.AI.NavMeshPath
UnityEngine.AI.ObstacleAvoidanceType
UnityEngine.AI.NavMeshAgent
UnityEngine.AI.NavMeshObstacleShape
UnityEngine.AI.NavMeshObstacle
UnityEngine.AI.OffMeshLinkType
UnityEngine.AI.OffMeshLinkData
UnityEngine.AI.OffMeshLink
UnityEngine.AI.NavMeshHit
UnityEngine.AI.NavMeshTriangulation
UnityEngine.AI.NavMesh