# Unity SIP 音频播放修复代码示例

## 📋 修复概述

基于问题诊断报告，提供具体的代码修复示例，解决音频播放无声问题。

---

## 🔧 修复代码实现

### 1. AudioExtrasSink.cs 核心修复

#### 1.1 改进的DelayedInitialization方法
```csharp
/// <summary>
/// 延迟初始化，确保正确配置音频播放参数
/// </summary>
private System.Collections.IEnumerator DelayedInitialization()
{
    yield return null;

    // 获取Unity系统采样率
    int systemSampleRate = AudioSettings.outputSampleRate;
    Debug.Log($"[AudioExtrasSink] Unity系统采样率: {systemSampleRate}Hz");
    Debug.Log($"[AudioExtrasSink] G.722解码输出采样率: {G722_PCM_SAMPLE_RATE}Hz");

    // 决定播放采样率策略
    if (systemSampleRate >= 44100) // 高质量系统
    {
        _unityPlaybackSampleRate = systemSampleRate;
        _needsResampling = true;
        Debug.Log($"[AudioExtrasSink] 启用重采样: {G722_PCM_SAMPLE_RATE}Hz → {systemSampleRate}Hz");
    }
    else if (systemSampleRate == G722_PCM_SAMPLE_RATE) // 完美匹配
    {
        _unityPlaybackSampleRate = G722_PCM_SAMPLE_RATE;
        _needsResampling = false;
        Debug.Log($"[AudioExtrasSink] 采样率完美匹配: {G722_PCM_SAMPLE_RATE}Hz");
    }
    else // 其他情况，使用系统采样率
    {
        _unityPlaybackSampleRate = systemSampleRate;
        _needsResampling = true;
        Debug.LogWarning($"[AudioExtrasSink] 非标准采样率，启用重采样: {G722_PCM_SAMPLE_RATE}Hz → {systemSampleRate}Hz");
    }

    Debug.Log($"[AudioExtrasSink] Unity DSP缓冲区大小: {AudioSettings.GetConfiguration().dspBufferSize}样本");

    // 配置AudioSource
    if (_audioSource != null)
    {
        _audioSource.playOnAwake = false;
        _audioSource.loop = true;
        _audioSource.spatialBlend = 0f; // 2D音频
        _audioSource.pitch = 1.0f;
        _audioSource.volume = _volume;
        _audioSource.priority = 64; // 中等优先级，不抢占其他重要音频
        _audioSource.bypassEffects = true; // 绕过效果以减少延迟
        _audioSource.bypassListenerEffects = true;
        _audioSource.bypassReverbZones = true;
        _audioSource.Stop();

        // 创建AudioClip，使用系统采样率
        int clipLengthSamples = _unityPlaybackSampleRate; // 1秒缓冲区
        _audioSource.clip = AudioClip.Create("SIPAudioPlayback", clipLengthSamples, CHANNELS, _unityPlaybackSampleRate, true, OnAudioRead, OnAudioSetPosition);

        Debug.Log($"[AudioExtrasSink] AudioClip创建成功:");
        Debug.Log($"  - 采样率: {_unityPlaybackSampleRate}Hz");
        Debug.Log($"  - 长度: {clipLengthSamples}样本 (1秒)");
        Debug.Log($"  - 声道: {CHANNELS}");
        
        _audioSource.Play();
        Debug.Log($"[AudioExtrasSink] AudioSource播放状态: {_audioSource.isPlaying}");
    }

    // 初始化重采样缓冲区
    if (_needsResampling)
    {
        InitializeResamplingBuffers();
    }

    // 初始化音频保存功能
    InitializeAudioSaving();
    
    // 启动音频状态监控
    StartCoroutine(AudioStatusMonitor());
}
```

#### 1.2 修复的GotAudioRtp方法
```csharp
/// <summary>
/// RTP解包后调用，输入G.722音频帧 - 修复版本
/// </summary>
public void GotAudioRtp(System.Net.IPEndPoint remoteEndPoint, uint ssrc, uint seqnum, uint timestamp, int payloadType, bool marker, byte[] payload)
{
    if (!_isStarted || payload == null || payload.Length == 0) 
    {
        Debug.LogWarning($"[AudioExtrasSink] RTP包被拒绝: started={_isStarted}, payload={(payload?.Length ?? 0)}字节");
        return;
    }

    // 检测丢包
    if (_firstPacketReceived)
    {
        uint expectedSeq = (_lastSequenceNumber + 1) & 0xFFFF;
        if (seqnum != expectedSeq)
        {
            uint lostPackets = (seqnum - expectedSeq) & 0xFFFF;
            if (lostPackets < 100) // 防止序列号回绕导致的误判
            {
                Debug.LogWarning($"[AudioExtrasSink] 检测到丢包: 期望={expectedSeq}, 实际={seqnum}, 丢失={lostPackets}帧");
                _stats.PacketLoss += lostPackets;
            }
        }
    }
    else
    {
        _firstPacketReceived = true;
        Debug.Log($"[AudioExtrasSink] 接收到第一个RTP包: Seq={seqnum}, PayloadType={payloadType}, Size={payload.Length}字节");
    }

    _lastSequenceNumber = seqnum;
    _stats.PacketsReceived++;

    // 使用G.722解码器解码音频数据
    try
    {
        // G.722解码：payload字节 -> PCM样本 (压缩比约2:1)
        var decodedSamples = new short[payload.Length * 2];
        int decodedLength = _g722Codec.Decode(_g722Decoder, decodedSamples, payload, payload.Length);

        if (decodedLength > 0)
        {
            // 只使用实际解码的样本
            var actualDecoded = new short[decodedLength];
            Array.Copy(decodedSamples, actualDecoded, decodedLength);

            // 修复：正确的PCM到float转换
            var floatData = new float[decodedLength];
            for (int i = 0; i < decodedLength; i++)
            {
                // 使用正确的除数和范围限制
                float sample = actualDecoded[i] / 32767.0f; // 16位有符号整数最大值
                floatData[i] = Mathf.Clamp(sample, -1.0f, 1.0f);
            }

            // 验证音频数据质量
            ValidateAudioData(floatData, seqnum);

            // 如果需要重采样，进行重采样处理
            if (_needsResampling)
            {
                floatData = ResampleAudio(floatData, G722_PCM_SAMPLE_RATE, _unityPlaybackSampleRate);
            }

            // 加入播放队列
            lock (_queueLock)
            {
                _audioQueue.Enqueue(floatData);

                // 智能队列管理：保持合理的缓冲区大小
                while (_audioQueue.Count > MAX_QUEUE_SIZE)
                {
                    var droppedFrame = _audioQueue.Dequeue();
                    _stats.FramesDropped++;
                    Debug.LogWarning($"[AudioExtrasSink] 队列溢出，丢弃音频帧 (队列大小: {_audioQueue.Count})");
                }
            }

            // 定期输出统计信息
            if (seqnum % 100 == 0)
            {
                lock (_queueLock)
                {
                    Debug.Log($"[AudioExtrasSink] 音频处理统计: Seq={seqnum}, 解码={decodedLength}样本, 队列={_audioQueue.Count}, 重采样={_needsResampling}");
                }
            }

            _stats.FramesDecoded++;
        }
        else
        {
            Debug.LogError($"[AudioExtrasSink] G.722解码失败: Seq={seqnum}, PayloadSize={payload.Length}, 返回长度={decodedLength}");
            _stats.DecodeErrors++;
        }
    }
    catch (Exception ex)
    {
        Debug.LogError($"[AudioExtrasSink] 音频解码异常: Seq={seqnum}, Error={ex.Message}");
        _stats.DecodeErrors++;
    }
}
```

#### 1.3 改进的OnAudioRead回调
```csharp
/// <summary>
/// Unity音频回调 - 改进版本，处理数据大小不匹配问题
/// </summary>
private void OnAudioRead(float[] data)
{
    _stats.AudioReadCalls++;
    int dataFilled = 0;

    lock (_queueLock)
    {
        // 尽可能填充Unity请求的数据
        while (dataFilled < data.Length && _audioQueue.Count > 0)
        {
            float[] audioFrame = _audioQueue.Dequeue();
            int remainingSpace = data.Length - dataFilled;
            int samplesToCopy = Math.Min(audioFrame.Length, remainingSpace);

            // 复制音频数据
            Array.Copy(audioFrame, 0, data, dataFilled, samplesToCopy);
            dataFilled += samplesToCopy;

            // 如果帧没有完全使用，处理剩余部分
            if (samplesToCopy < audioFrame.Length)
            {
                // 创建剩余数据帧并放回队列前端
                float[] remainingFrame = new float[audioFrame.Length - samplesToCopy];
                Array.Copy(audioFrame, samplesToCopy, remainingFrame, 0, remainingFrame.Length);
                
                // 使用临时列表实现队列前端插入
                var tempList = new List<float[]> { remainingFrame };
                while (_audioQueue.Count > 0)
                {
                    tempList.Add(_audioQueue.Dequeue());
                }
                
                foreach (var frame in tempList)
                {
                    _audioQueue.Enqueue(frame);
                }
            }
        }

        // 记录队列状态
        _currentQueueSize = _audioQueue.Count;
    }

    // 如果还有空间未填充，用静音填充
    if (dataFilled < data.Length)
    {
        Array.Clear(data, dataFilled, data.Length - dataFilled);
        _stats.SilentFrames++;
    }
    else
    {
        _stats.AudioFrames++;
    }

    // 更新播放状态
    if (dataFilled > 0)
    {
        _lastAudioPlayTime = Time.realtimeSinceStartup;
        _hasPlayedAudio = true;
    }

    // 保存播放音频数据用于调试
    if (_enableAudioSaving)
    {
        SavePlaybackAudio(data);
    }

    // 验证输出音频数据
    if (dataFilled > 0)
    {
        ValidateOutputAudio(data, dataFilled);
    }
}
```

#### 1.4 音频重采样实现
```csharp
/// <summary>
/// 简单线性重采样实现
/// </summary>
private float[] ResampleAudio(float[] input, int inputSampleRate, int outputSampleRate)
{
    if (inputSampleRate == outputSampleRate)
        return input;

    double ratio = (double)inputSampleRate / outputSampleRate;
    int outputLength = (int)(input.Length / ratio);
    float[] output = new float[outputLength];

    for (int i = 0; i < outputLength; i++)
    {
        double sourceIndex = i * ratio;
        int index1 = (int)sourceIndex;
        int index2 = Math.Min(index1 + 1, input.Length - 1);
        double fraction = sourceIndex - index1;

        // 线性插值
        if (index1 < input.Length)
        {
            output[i] = (float)(input[index1] * (1.0 - fraction) + input[index2] * fraction);
        }
    }

    return output;
}
```

#### 1.5 音频数据验证方法
```csharp
/// <summary>
/// 验证解码后的音频数据质量
/// </summary>
private void ValidateAudioData(float[] audioData, uint sequenceNumber)
{
    if (audioData == null || audioData.Length == 0)
    {
        Debug.LogError($"[AudioExtrasSink] 音频数据为空: Seq={sequenceNumber}");
        return;
    }

    float maxLevel = 0f;
    float avgLevel = 0f;
    int clippedSamples = 0;

    for (int i = 0; i < audioData.Length; i++)
    {
        float abs = Math.Abs(audioData[i]);
        maxLevel = Math.Max(maxLevel, abs);
        avgLevel += abs;

        if (abs > 1.0f)
        {
            clippedSamples++;
        }
    }

    avgLevel /= audioData.Length;

    // 记录音频质量统计
    _stats.MaxAudioLevel = Math.Max(_stats.MaxAudioLevel, maxLevel);
    _stats.AvgAudioLevel = (_stats.AvgAudioLevel + avgLevel) / 2.0f;

    // 检查异常情况
    if (clippedSamples > 0)
    {
        Debug.LogError($"[AudioExtrasSink] 音频削波检测: Seq={sequenceNumber}, 削波样本={clippedSamples}/{audioData.Length}, 最大电平={maxLevel:F3}");
    }

    if (avgLevel < 0.001f)
    {
        Debug.LogWarning($"[AudioExtrasSink] 音频电平过低: Seq={sequenceNumber}, 平均电平={avgLevel:F6}");
    }

    if (maxLevel > 0.95f)
    {
        Debug.LogWarning($"[AudioExtrasSink] 音频电平过高: Seq={sequenceNumber}, 最大电平={maxLevel:F3}");
    }
}
```

#### 1.6 音频状态监控协程
```csharp
/// <summary>
/// 音频播放状态监控协程
/// </summary>
private System.Collections.IEnumerator AudioStatusMonitor()
{
    while (_isStarted)
    {
        yield return new WaitForSeconds(5.0f); // 每5秒检查一次

        // 检查音频播放状态
        float timeSinceLastAudio = Time.realtimeSinceStartup - _lastAudioPlayTime;
        bool audioSourcePlaying = _audioSource != null && _audioSource.isPlaying;

        Debug.Log($"[AudioExtrasSink] 音频状态监控:");
        Debug.Log($"  - AudioSource播放: {audioSourcePlaying}");
        Debug.Log($"  - 距离上次音频: {timeSinceLastAudio:F1}秒");
        Debug.Log($"  - 当前队列大小: {_currentQueueSize}");
        Debug.Log($"  - 已播放音频: {_hasPlayedAudio}");
        Debug.Log($"  - 统计信息: 接收={_stats.PacketsReceived}, 解码={_stats.FramesDecoded}, 播放={_stats.AudioFrames}, 静音={_stats.SilentFrames}");

        // 检查异常情况
        if (!audioSourcePlaying && _isStarted)
        {
            Debug.LogError("[AudioExtrasSink] AudioSource未在播放，尝试重新启动");
            if (_audioSource != null)
            {
                _audioSource.Play();
            }
        }

        if (timeSinceLastAudio > 10.0f && _stats.PacketsReceived > 0)
        {
            Debug.LogError($"[AudioExtrasSink] 长时间无音频播放 ({timeSinceLastAudio:F1}秒)，可能存在问题");
        }

        if (_currentQueueSize == 0 && _stats.PacketsReceived > 100)
        {
            Debug.LogWarning("[AudioExtrasSink] 音频队列为空，但已接收大量包，可能存在处理问题");
        }
    }
}
```

### 2. 添加音频统计结构
```csharp
/// <summary>
/// 音频播放统计信息
/// </summary>
private struct AudioStats
{
    public int PacketsReceived;
    public int FramesDecoded;
    public int FramesDropped;
    public int DecodeErrors;
    public int AudioReadCalls;
    public int AudioFrames;
    public int SilentFrames;
    public int PacketLoss;
    public float MaxAudioLevel;
    public float AvgAudioLevel;
}

private AudioStats _stats = new AudioStats();
```

### 3. 添加必要的字段
```csharp
// 在AudioExtrasSink类中添加这些字段
private bool _needsResampling = false;
private float _lastAudioPlayTime = 0f;
private bool _hasPlayedAudio = false;
private int _currentQueueSize = 0;
```

---

## 🧪 调试和验证代码

### 1. 音频环境检查方法
```csharp
/// <summary>
/// 检查音频环境配置
/// </summary>
public void CheckAudioEnvironment()
{
    Debug.Log("=== Unity音频环境检查 ===");
    
    // Unity音频设置
    var config = AudioSettings.GetConfiguration();
    Debug.Log($"Unity音频配置:");
    Debug.Log($"  - 输出采样率: {AudioSettings.outputSampleRate}Hz");
    Debug.Log($"  - DSP缓冲区: {config.dspBufferSize}样本");
    Debug.Log($"  - 扬声器模式: {config.speakerMode}");
    Debug.Log($"  - 实时模式: {config.numRealVoices}");

    // AudioSource状态
    if (_audioSource != null)
    {
        Debug.Log($"AudioSource状态:");
        Debug.Log($"  - 是否播放: {_audioSource.isPlaying}");
        Debug.Log($"  - 音量: {_audioSource.volume}");
        Debug.Log($"  - 优先级: {_audioSource.priority}");
        Debug.Log($"  - 空间混合: {_audioSource.spatialBlend}");
        
        if (_audioSource.clip != null)
        {
            Debug.Log($"  - Clip采样率: {_audioSource.clip.frequency}Hz");
            Debug.Log($"  - Clip长度: {_audioSource.clip.length:F2}秒");
            Debug.Log($"  - Clip声道: {_audioSource.clip.channels}");
            Debug.Log($"  - Clip样本数: {_audioSource.clip.samples}");
        }
        else
        {
            Debug.LogError("  - AudioClip未设置！");
        }
    }
    else
    {
        Debug.LogError("AudioSource未设置！");
    }

    // G.722解码器状态
    Debug.Log($"G.722解码器:");
    Debug.Log($"  - 解码器已初始化: {_g722Codec != null && _g722Decoder != null}");
    Debug.Log($"  - 输入采样率: {G722_RTP_CLOCK_RATE}Hz (RTP时钟)");
    Debug.Log($"  - 输出采样率: {G722_PCM_SAMPLE_RATE}Hz (PCM)");
    Debug.Log($"  - 播放采样率: {_unityPlaybackSampleRate}Hz");
    Debug.Log($"  - 需要重采样: {_needsResampling}");

    Debug.Log("=== 检查完成 ===");
}
```

### 2. 手动音频测试方法
```csharp
/// <summary>
/// 生成测试音频信号
/// </summary>
public void GenerateTestAudio()
{
    Debug.Log("[AudioExtrasSink] 生成测试音频信号...");
    
    // 生成1kHz正弦波测试信号
    int sampleCount = G722_PCM_SAMPLE_RATE / 50; // 20ms的样本数
    float[] testAudio = new float[sampleCount];
    
    for (int i = 0; i < sampleCount; i++)
    {
        float time = (float)i / G722_PCM_SAMPLE_RATE;
        testAudio[i] = 0.3f * Mathf.Sin(2 * Mathf.PI * 1000 * time); // 1kHz, 30%音量
    }
    
    // 如果需要重采样
    if (_needsResampling)
    {
        testAudio = ResampleAudio(testAudio, G722_PCM_SAMPLE_RATE, _unityPlaybackSampleRate);
    }
    
    // 直接加入播放队列
    lock (_queueLock)
    {
        _audioQueue.Enqueue(testAudio);
    }
    
    Debug.Log($"[AudioExtrasSink] 测试音频已加入队列，样本数: {testAudio.Length}");
}
```

---

## 📋 使用说明

### 1. 应用修复代码
1. 备份原始的`AudioExtrasSink.cs`文件
2. 应用上述修复代码
3. 添加必要的字段和方法
4. 重新编译项目

### 2. 调试步骤
1. 在Unity Console中启用详细日志
2. 运行项目并建立SIP连接
3. 调用`CheckAudioEnvironment()`检查配置
4. 使用`GenerateTestAudio()`测试播放功能
5. 观察音频状态监控输出

### 3. 验证清单
- [ ] Unity Console显示音频配置信息
- [ ] AudioSource显示为播放状态
- [ ] 音频队列有数据流入
- [ ] 没有解码错误日志
- [ ] 音频电平在合理范围内

---

*修复代码版本：1.0*  
*最后更新：2025年7月15日*
