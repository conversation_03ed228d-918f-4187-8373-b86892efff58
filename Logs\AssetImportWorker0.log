Using pre-set license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.26200) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16167 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/司导/SIP2.0
-logFile
Logs/AssetImportWorker0.log
-srvPort
1637
Successfully changed project path to: D:/Unity/司导/SIP2.0
D:/Unity/司导/SIP2.0
Using Asset Import Pipeline V2.
Player connection [19240] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-4TNQCCV1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [19240] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-4TNQCCV1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.Refreshing native plugins compatible for Editor in 71.57 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/司导/SIP2.0/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x9a49)
    Vendor:   
    VRAM:     8083 MB
    Driver:   32.0.101.5990
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56340
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.002182 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 66.39 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.937 seconds
Domain Reload Profiling:
	ReloadAssembly (937ms)
		BeginReloadAssembly (149ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (692ms)
			LoadAssemblies (151ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (223ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (41ms)
			SetupLoadedEditorAssemblies (275ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (23ms)
				ProcessInitializeOnLoadAttributes (130ms)
				ProcessInitializeOnLoadMethodAttributes (48ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.058739 seconds.
