Using pre-set license
Built from '2020.3/china_unity/release' branch; Version is '2020.3.48f1c1 (06fbdfbf16e3) revision 457695'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.26200) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16167 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
D:\Unity\2020.3.48f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/司导/SIP2.0
-logFile
Logs/AssetImportWorker0.log
-srvPort
1637
Successfully changed project path to: D:/Unity/司导/SIP2.0
D:/Unity/司导/SIP2.0
Using Asset Import Pipeline V2.
Player connection [19240] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-4TNQCCV1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [19240] Host "[IP] ************* [Port] ********** [Flags] 2 [Guid] ********** [EditorId] ********** [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-4TNQCCV1) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

AS: AutoStreaming module initializing.Refreshing native plugins compatible for Editor in 71.57 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.48f1c1 (06fbdfbf16e3)
[Subsystems] Discovering subsystems at path D:/Unity/2020.3.48f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/司导/SIP2.0/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x9a49)
    Vendor:   
    VRAM:     8083 MB
    Driver:   32.0.101.5990
Initialize mono
Mono path[0] = 'D:/Unity/2020.3.48f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56340
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.002182 seconds.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 66.39 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.937 seconds
Domain Reload Profiling:
	ReloadAssembly (937ms)
		BeginReloadAssembly (149ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (692ms)
			LoadAssemblies (151ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (223ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (41ms)
			SetupLoadedEditorAssemblies (275ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (67ms)
				BeforeProcessingInitializeOnLoad (23ms)
				ProcessInitializeOnLoadAttributes (130ms)
				ProcessInitializeOnLoadMethodAttributes (48ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.058739 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  3.600 seconds
Domain Reload Profiling:
	ReloadAssembly (3601ms)
		BeginReloadAssembly (301ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (31ms)
		EndReloadAssembly (3220ms)
			LoadAssemblies (287ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (1418ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (122ms)
			SetupLoadedEditorAssemblies (1287ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (160ms)
				ProcessInitializeOnLoadAttributes (1093ms)
				ProcessInitializeOnLoadMethodAttributes (15ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.13 seconds
Refreshing native plugins compatible for Editor in 3.45 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1489 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 72.3 MB.
System memory in use after: 72.3 MB.

Unloading 109 unused Assets to reduce memory usage. Loaded Objects now: 1838.
Total: 6.088900 ms (FindLiveObjects: 0.225200 ms CreateObjectMapping: 0.153400 ms MarkObjects: 5.419000 ms  DeleteObjects: 0.289200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Scripts/test
  artifactKey: Guid(288f9c7bd89b0314e864e0abe5626dc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/test using Guid(288f9c7bd89b0314e864e0abe5626dc8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '05ce71342c0ae7f64a65930fee909e09') in 0.027874 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.001286 seconds.
  path: Assets/Scripts/Tests
  artifactKey: Guid(809ea5c642e8ed949a5be0d1d16f0911) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Tests using Guid(809ea5c642e8ed949a5be0d1d16f0911) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4279d5dddc4944dd3d22475102b3bbf7') in 0.020724 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.087209 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.97 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.946 seconds
Domain Reload Profiling:
	ReloadAssembly (1947ms)
		BeginReloadAssembly (247ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (61ms)
		EndReloadAssembly (1604ms)
			LoadAssemblies (263ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (579ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (53ms)
			SetupLoadedEditorAssemblies (663ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (118ms)
				ProcessInitializeOnLoadAttributes (519ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.93 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 70.9 MB.
System memory in use after: 71.0 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1841.
Total: 4.147400 ms (FindLiveObjects: 0.137500 ms CreateObjectMapping: 0.044400 ms MarkObjects: 3.839400 ms  DeleteObjects: 0.124600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.039690 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.87 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.239 seconds
Domain Reload Profiling:
	ReloadAssembly (1240ms)
		BeginReloadAssembly (183ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1007ms)
			LoadAssemblies (175ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (342ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (31ms)
			SetupLoadedEditorAssemblies (450ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (347ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.78 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 70.9 MB.
System memory in use after: 71.0 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1844.
Total: 3.423200 ms (FindLiveObjects: 0.115300 ms CreateObjectMapping: 0.038900 ms MarkObjects: 3.153700 ms  DeleteObjects: 0.113600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.042410 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.70 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.301 seconds
Domain Reload Profiling:
	ReloadAssembly (1302ms)
		BeginReloadAssembly (185ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (44ms)
		EndReloadAssembly (1062ms)
			LoadAssemblies (200ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (355ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (466ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (87ms)
				ProcessInitializeOnLoadAttributes (361ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.02 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.0 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1847.
Total: 2.623400 ms (FindLiveObjects: 0.097600 ms CreateObjectMapping: 0.029500 ms MarkObjects: 2.403400 ms  DeleteObjects: 0.091600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.037998 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.235 seconds
Domain Reload Profiling:
	ReloadAssembly (1238ms)
		BeginReloadAssembly (157ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (42ms)
		EndReloadAssembly (1025ms)
			LoadAssemblies (165ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (381ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (419ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (326ms)
				ProcessInitializeOnLoadMethodAttributes (7ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.66 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1850.
Total: 3.324700 ms (FindLiveObjects: 0.101600 ms CreateObjectMapping: 0.046400 ms MarkObjects: 3.071200 ms  DeleteObjects: 0.104000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.042468 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.280 seconds
Domain Reload Profiling:
	ReloadAssembly (1281ms)
		BeginReloadAssembly (179ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (46ms)
		EndReloadAssembly (1030ms)
			LoadAssemblies (176ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (323ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (480ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (380ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.66 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1853.
Total: 2.702100 ms (FindLiveObjects: 0.110800 ms CreateObjectMapping: 0.029700 ms MarkObjects: 2.461000 ms  DeleteObjects: 0.099400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.109843 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.65 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.220 seconds
Domain Reload Profiling:
	ReloadAssembly (2221ms)
		BeginReloadAssembly (274ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (54ms)
		EndReloadAssembly (1839ms)
			LoadAssemblies (350ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (587ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (65ms)
			SetupLoadedEditorAssemblies (809ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (11ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (2ms)
				BeforeProcessingInitializeOnLoad (141ms)
				ProcessInitializeOnLoadAttributes (634ms)
				ProcessInitializeOnLoadMethodAttributes (16ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1856.
Total: 6.533900 ms (FindLiveObjects: 0.212400 ms CreateObjectMapping: 0.056800 ms MarkObjects: 6.103000 ms  DeleteObjects: 0.158300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.053953 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.68 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.315 seconds
Domain Reload Profiling:
	ReloadAssembly (1316ms)
		BeginReloadAssembly (179ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (44ms)
		EndReloadAssembly (1070ms)
			LoadAssemblies (192ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (395ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (438ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (85ms)
				ProcessInitializeOnLoadAttributes (335ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.67 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1859.
Total: 3.528900 ms (FindLiveObjects: 0.177100 ms CreateObjectMapping: 0.069100 ms MarkObjects: 3.167300 ms  DeleteObjects: 0.114500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.068675 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.12 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.586 seconds
Domain Reload Profiling:
	ReloadAssembly (1587ms)
		BeginReloadAssembly (188ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1334ms)
			LoadAssemblies (198ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (436ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (42ms)
			SetupLoadedEditorAssemblies (635ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (106ms)
				ProcessInitializeOnLoadAttributes (509ms)
				ProcessInitializeOnLoadMethodAttributes (9ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.73 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1862.
Total: 2.604500 ms (FindLiveObjects: 0.101900 ms CreateObjectMapping: 0.028500 ms MarkObjects: 2.373300 ms  DeleteObjects: 0.099700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.070490 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.85 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.746 seconds
Domain Reload Profiling:
	ReloadAssembly (1747ms)
		BeginReloadAssembly (211ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1465ms)
			LoadAssemblies (240ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (500ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (639ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (10ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (109ms)
				ProcessInitializeOnLoadAttributes (503ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.92 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1865.
Total: 5.355400 ms (FindLiveObjects: 1.272700 ms CreateObjectMapping: 0.268200 ms MarkObjects: 3.642600 ms  DeleteObjects: 0.168900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.076765 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.93 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.309 seconds
Domain Reload Profiling:
	ReloadAssembly (1311ms)
		BeginReloadAssembly (235ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (52ms)
		EndReloadAssembly (993ms)
			LoadAssemblies (254ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (326ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (419ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (71ms)
				ProcessInitializeOnLoadAttributes (332ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.64 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1868.
Total: 2.667800 ms (FindLiveObjects: 0.104300 ms CreateObjectMapping: 0.031600 ms MarkObjects: 2.428300 ms  DeleteObjects: 0.102600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.046446 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.64 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.313 seconds
Domain Reload Profiling:
	ReloadAssembly (1314ms)
		BeginReloadAssembly (196ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (44ms)
		EndReloadAssembly (1042ms)
			LoadAssemblies (216ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (363ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (422ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (323ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.66 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1871.
Total: 2.904400 ms (FindLiveObjects: 0.104800 ms CreateObjectMapping: 0.029200 ms MarkObjects: 2.633400 ms  DeleteObjects: 0.135600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.048487 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.75 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.212 seconds
Domain Reload Profiling:
	ReloadAssembly (1214ms)
		BeginReloadAssembly (194ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (943ms)
			LoadAssemblies (181ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (313ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (37ms)
			SetupLoadedEditorAssemblies (405ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (70ms)
				ProcessInitializeOnLoadAttributes (317ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.73 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1874.
Total: 2.695600 ms (FindLiveObjects: 0.101300 ms CreateObjectMapping: 0.046400 ms MarkObjects: 2.456900 ms  DeleteObjects: 0.089700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.073821 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.79 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.835 seconds
Domain Reload Profiling:
	ReloadAssembly (1836ms)
		BeginReloadAssembly (275ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (55ms)
		EndReloadAssembly (1451ms)
			LoadAssemblies (294ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (381ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (41ms)
			SetupLoadedEditorAssemblies (784ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (107ms)
				ProcessInitializeOnLoadAttributes (657ms)
				ProcessInitializeOnLoadMethodAttributes (9ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.69 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1877.
Total: 2.859400 ms (FindLiveObjects: 0.109800 ms CreateObjectMapping: 0.069000 ms MarkObjects: 2.571400 ms  DeleteObjects: 0.107600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.048720 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.70 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.244 seconds
Domain Reload Profiling:
	ReloadAssembly (1245ms)
		BeginReloadAssembly (153ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (1042ms)
			LoadAssemblies (158ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (311ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (32ms)
			SetupLoadedEditorAssemblies (492ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (361ms)
				ProcessInitializeOnLoadMethodAttributes (28ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.67 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1880.
Total: 2.903000 ms (FindLiveObjects: 0.114600 ms CreateObjectMapping: 0.050600 ms MarkObjects: 2.590400 ms  DeleteObjects: 0.146300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.066399 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.82 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.924 seconds
Domain Reload Profiling:
	ReloadAssembly (1925ms)
		BeginReloadAssembly (263ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (1570ms)
			LoadAssemblies (317ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (505ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (52ms)
			SetupLoadedEditorAssemblies (693ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (113ms)
				ProcessInitializeOnLoadAttributes (557ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.05 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.0 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1883.
Total: 4.885800 ms (FindLiveObjects: 0.377900 ms CreateObjectMapping: 0.179300 ms MarkObjects: 4.114600 ms  DeleteObjects: 0.211800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.038749 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.95 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.398 seconds
Domain Reload Profiling:
	ReloadAssembly (1399ms)
		BeginReloadAssembly (141ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (1205ms)
			LoadAssemblies (151ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (468ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (36ms)
			SetupLoadedEditorAssemblies (487ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (91ms)
				ProcessInitializeOnLoadAttributes (368ms)
				ProcessInitializeOnLoadMethodAttributes (14ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (3ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.65 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1886.
Total: 2.644500 ms (FindLiveObjects: 0.103200 ms CreateObjectMapping: 0.028200 ms MarkObjects: 2.402500 ms  DeleteObjects: 0.109400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.056810 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.82 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.893 seconds
Domain Reload Profiling:
	ReloadAssembly (1895ms)
		BeginReloadAssembly (258ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (58ms)
		EndReloadAssembly (1521ms)
			LoadAssemblies (289ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (493ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (687ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (8ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (109ms)
				ProcessInitializeOnLoadAttributes (551ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.91 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1889.
Total: 3.753600 ms (FindLiveObjects: 0.139400 ms CreateObjectMapping: 0.032700 ms MarkObjects: 3.461300 ms  DeleteObjects: 0.105700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.045203 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.01 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.355 seconds
Domain Reload Profiling:
	ReloadAssembly (1356ms)
		BeginReloadAssembly (153ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (1144ms)
			LoadAssemblies (159ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (439ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (40ms)
			SetupLoadedEditorAssemblies (472ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (5ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (378ms)
				ProcessInitializeOnLoadMethodAttributes (7ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.69 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1892.
Total: 2.784300 ms (FindLiveObjects: 0.100200 ms CreateObjectMapping: 0.031700 ms MarkObjects: 2.560200 ms  DeleteObjects: 0.090700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.060840 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.88 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.606 seconds
Domain Reload Profiling:
	ReloadAssembly (1608ms)
		BeginReloadAssembly (256ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (82ms)
		EndReloadAssembly (1281ms)
			LoadAssemblies (253ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (410ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (50ms)
			SetupLoadedEditorAssemblies (534ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (7ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (96ms)
				ProcessInitializeOnLoadAttributes (415ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (3ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.08 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1895.
Total: 3.228500 ms (FindLiveObjects: 0.126300 ms CreateObjectMapping: 0.047600 ms MarkObjects: 2.921300 ms  DeleteObjects: 0.132200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.043326 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.85 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.286 seconds
Domain Reload Profiling:
	ReloadAssembly (1287ms)
		BeginReloadAssembly (162ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (37ms)
		EndReloadAssembly (1067ms)
			LoadAssemblies (177ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (350ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (33ms)
			SetupLoadedEditorAssemblies (486ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (387ms)
				ProcessInitializeOnLoadMethodAttributes (7ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.66 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.1 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1898.
Total: 2.561400 ms (FindLiveObjects: 0.102400 ms CreateObjectMapping: 0.030300 ms MarkObjects: 2.330600 ms  DeleteObjects: 0.097100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.059538 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.26 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.021 seconds
Domain Reload Profiling:
	ReloadAssembly (2022ms)
		BeginReloadAssembly (216ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (49ms)
		EndReloadAssembly (1728ms)
			LoadAssemblies (271ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (575ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (776ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (132ms)
				ProcessInitializeOnLoadAttributes (618ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (3ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.07 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.2 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1901.
Total: 3.745600 ms (FindLiveObjects: 0.128400 ms CreateObjectMapping: 0.032700 ms MarkObjects: 3.438800 ms  DeleteObjects: 0.144000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.171379 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.66 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.371 seconds
Domain Reload Profiling:
	ReloadAssembly (1372ms)
		BeginReloadAssembly (203ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (47ms)
		EndReloadAssembly (1105ms)
			LoadAssemblies (205ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (339ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (34ms)
			SetupLoadedEditorAssemblies (497ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (6ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (97ms)
				ProcessInitializeOnLoadAttributes (382ms)
				ProcessInitializeOnLoadMethodAttributes (8ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.04 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.2 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1904.
Total: 4.457500 ms (FindLiveObjects: 0.171800 ms CreateObjectMapping: 0.074400 ms MarkObjects: 4.087600 ms  DeleteObjects: 0.122200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.055722 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.87 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.438 seconds
Domain Reload Profiling:
	ReloadAssembly (1439ms)
		BeginReloadAssembly (170ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (44ms)
		EndReloadAssembly (1204ms)
			LoadAssemblies (192ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (424ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (41ms)
			SetupLoadedEditorAssemblies (477ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (9ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (356ms)
				ProcessInitializeOnLoadMethodAttributes (9ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (2ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.03 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.2 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1907.
Total: 4.628000 ms (FindLiveObjects: 0.151200 ms CreateObjectMapping: 0.058300 ms MarkObjects: 4.225900 ms  DeleteObjects: 0.190800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.123477 seconds.
Begin MonoManager ReloadAssembly
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.21 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.235 seconds
Domain Reload Profiling:
	ReloadAssembly (2237ms)
		BeginReloadAssembly (318ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (10ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (82ms)
		EndReloadAssembly (1817ms)
			LoadAssemblies (309ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (603ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (771ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (12ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (137ms)
				ProcessInitializeOnLoadAttributes (596ms)
				ProcessInitializeOnLoadMethodAttributes (18ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (4ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.03 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 1471 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 71.1 MB.
System memory in use after: 71.2 MB.

Unloading 104 unused Assets to reduce memory usage. Loaded Objects now: 1910.
Total: 4.545500 ms (FindLiveObjects: 0.549200 ms CreateObjectMapping: 0.233000 ms MarkObjects: 3.494500 ms  DeleteObjects: 0.266700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
