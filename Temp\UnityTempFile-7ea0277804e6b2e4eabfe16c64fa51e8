/target:library
/out:Temp/Unity.PlasticSCM.Editor.dll
/nowarn:0169
/nowarn:0649
/refout:Temp/Unity.PlasticSCM.Editor.dll.ref
/deterministic
/debug:portable
/optimize+
/nostdlib+
/preferreduilang:en-US
/langversion:8.0
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.collab-proxy@2.0.4/Lib/Editor/PlasticSCM/unityplastic.dll"
/reference:"D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AutoStreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CloudFoundationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll
/reference:D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll
/define:CSHARP_7_3_OR_NEWER
/define:CSHARP_7_OR_LATER
/define:DEBUG
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_BURST_AOT
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_CLOUD_FEATURES
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLUSTERINPUT
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:ENABLE_LOCALIZATION
/define:ENABLE_LZMA
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_UNITYTLS
/define:ENABLE_MICROPHONE
/define:ENABLE_MONO
/define:ENABLE_MONO_BDWGC
/define:ENABLE_MOVIES
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_NETWORK
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_PHYSICS
/define:ENABLE_PROFILER
/define:ENABLE_RUNTIME_GI
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_UNET
/define:ENABLE_UNITYEVENTS
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
/define:ENABLE_VIDEO
/define:ENABLE_VIRTUALTEXTURING
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_WWW
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:INCLUDE_DYNAMIC_GI
/define:NET_4_6
/define:PLATFORM_ARCH_64
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:PLATFORM_SUPPORTS_MONO
/define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
/define:RENDER_SOFTWARE_CURSOR
/define:TRACE
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:UNITY_2020
/define:UNITY_2020_1_OR_NEWER
/define:UNITY_2020_2_OR_NEWER
/define:UNITY_2020_3
/define:UNITY_2020_3_48
/define:UNITY_2020_3_OR_NEWER
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_64
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_IG
/define:UNITY_EDITOR_WIN
/define:UNITY_INCLUDE_TESTS
/define:UNITY_STANDALONE
/define:UNITY_STANDALONE_WIN
/define:UNITY_TEAM_LICENSE
/define:UNITY_UGP_API
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ApplicationDataPath.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssemblyInfo.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetFilesFilterPatternsMenuBuilder.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetMenuItems.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetMenuOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\AssetsSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialogOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetMenu\ProjectViewAssetSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\AssetStatus.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\AssetStatusCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\BuildPathDictionary.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\LocalStatusCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\LockStatusCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\RemoteStatusCache.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\Cache\SearchLocks.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetOverlays\DrawAssetOverlay.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\AssetsPath.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\GetSelectedPaths.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\LoadAsset.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\AssetModificationProcessor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\AssetPostprocessor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\AssetsProcessor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\PlasticAssetsProcessor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\Processor\WorkspaceOperationsMonitor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\ProjectPath.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\RefreshAsset.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\RepaintInspector.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AssetsUtils\SaveAssets.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\AutoRefresh.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CheckWorkspaceTreeNodeStatus.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\CloudProjectDownloader.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\CommandLineArguments.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\DownloadRepositoryOperation.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CloudProjectDownloader\ParseArguments.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabMigration\MigrateCollabProject.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabMigration\MigrationDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabMigration\MigrationProgressRender.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\CollabPlugin.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\AutoConfig.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\ChannelCertificateUiImpl.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\AutoLogin.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\CloudEditionWelcomeWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\OrganizationPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInWithEmailPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\WaitingSignInPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\ConfigurePartialWorkspace.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CredentialsDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\CredentialsUIImpl.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\EncryptionConfigurationDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\MissingEncryptionPasswordPromptHandler.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\SSOCredentialsDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\TeamEdition\TeamEditionConfigurationWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\ToolConfig.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Configuration\WriteLogConfiguration.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\CheckinProgress.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\GenericProgress.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\IncomingChangesNotifier.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\ProgressOperationHandler.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateProgress.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportLineListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\DrawGuiModeSwitcher.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\EnumExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\FindWorkspace.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\GetRelativePath.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\CheckinProgress.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\IncomingChangesNotifier.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\ProgressOperationHandler.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateProgress.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\ErrorListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\BuildFormattedHelp.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\DrawHelpPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpData.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpFormat.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpLink.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpLinkData.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\HelpPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Help\TestingHelpData.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Inspector\DrawInspectorOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Inspector\InspectorAssetSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\MetaPath.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\NewIncomingChanges.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ParentWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticApp.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticConnectionMonitor.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticMenuItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticNotification.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticPlugin.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticPluginIsEnabledPreference.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticProjectSettingsProvider.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\PlasticWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ProjectWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\QueryVisualElementsExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\SceneView\DrawSceneOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\SetupCloudProjectId.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\SwitchModeConfirmationDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\BringWindowToFront.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\FindTool.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\IsExeAvailable.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\LaunchInstaller.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\LaunchTool.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Tool\ToolConstants.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Avatar\ApplyCircleMask.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Avatar\AvatarImages.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Avatar\GetAvatar.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\BoolSetting.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\CloseWindowIfOpened.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\CooldownWindowDelayer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DockEditorWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawActionButton.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawActionHelpBox.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawActionToolbar.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawSearchField.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawSplitter.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawTextBlockWithEndLink.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DrawUserIcon.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\DropDownTextField.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorDispatcher.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorProgressBar.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorProgressControls.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorVersion.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EditorWindowFocus.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\EnumPopupSetting.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\FindEditorWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GUIActionRunner.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GUISpace.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GetPlasticShortcut.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\GuiEnabled.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\HandleMenuItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Images.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\MeasureMaxWidth.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Message\DrawDialogIcon.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Message\PlasticQuestionAlert.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\OverlayRect.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\PlasticDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\PlasticSplitterGUILayout.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForDialogs.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForMigration.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\DrawProgressForViews.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\OperationProgressData.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\ProgressControlsForDialogs.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\ProgressControlsForMigration.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Progress\ProgressControlsForViews.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\ResponseType.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\RunModal.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\ScreenResolution.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\ShowWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\SortOrderComparer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\StatusBar\IncomingChangesNotification.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\StatusBar\NotificationBar.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\StatusBar\StatusBar.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\TabButton.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\DrawTreeViewEmptyState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\DrawTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\GetChangesOverlayIcon.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\ListViewItemIds.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TableViewOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TreeHeaderColumns.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TreeHeaderSettings.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\Tree\TreeViewItemIds.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UIElements\LoadingSpinner.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UIElements\ProgressControlsForDialogs.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UIElements\UIElementsExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityConstants.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityEvents.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityMenuItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityPlasticGuiMessage.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityPlasticTimer.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityStyles.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UI\UnityThreadWaiter.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\UnityConfigurationChecker.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\VCSPlugin.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\ViewSwitcher.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\BranchesViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\CreateBranchDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Branch\Dialogs\RenameBranchDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsTab_Operations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\ChangesetsViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\DateFilter.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Changesets\LaunchDiffOperations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\ConfirmContinueWithPendingChangesDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceViewState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\CreateRepositoryDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryExplorerDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\DrawCreateWorkspaceView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\CreateWorkspace\ValidRepositoryName.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\ChangeCategoryTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\ClientDiffTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\Dialogs\GetRestorePathDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffTreeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\DiffTreeViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\GetClientDiffInfos.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\MergeCategoryTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Diff\UnityDiffTree.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\DownloadPlasticExeWindow.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\FileSystemOperation.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryListViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistorySelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\HistoryTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\History\SaveAction.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeCategoryTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\ConflictResolutionState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\DrawDirectoryResolutionPanel.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsCurrent.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsResolved.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Developer\UnityIncomingChangesTree.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\DrawIncomingChangesOverview.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeCategoryTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\Gluon\UnityIncomingChangesTree.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\IncomingChanges\IIncomingChangesTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\ChangeCategoryTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\ChangeTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\ChangelistTreeViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Changelists\ChangelistMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Changelists\MoveToChangelistMenuBuilder.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CheckinConflictsDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CreateChangelistDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\DependenciesDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\EmptyCheckinMessageDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\FilterRulesConfirmationDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchCheckinConflictsDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchDependenciesDialog.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\DrawCommentTextArea.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\FilesFilterPatternsMenuBuilder.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesMultiColumnHeader.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesSelection.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab_Operations.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeHeaderState.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewPendingChangeMenu.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinkListViewItem.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinksListView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\PendingChanges\UnityPendingChangesTree.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\DownloadAndInstallOperation.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\GetInstallerTmpFileName.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\MacOSConfigWorkaround.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\Views\Welcome\WelcomeView.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\VisualElementExtensions.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\ChangesetFromCollabCommitResponse.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\CredentialsResponse.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\CurrentUserAdminCheckResponse.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\IsCollabProjectMigratedResponse.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\OrganizationCredentials.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\TokenExchangeResponse.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WebApi\WebRestApiClient.cs"
"D:\Unity\司导\SIP2.0\Library\PackageCache\com.unity.collab-proxy@2.0.4\Editor\PlasticSCM\WorkspaceWindow.cs"
