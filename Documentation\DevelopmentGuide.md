
# 项目开发文档

## 1. 项目概述

本项目是一个基于 Unity 和 SIPSorcery 库实现的 SIP 客户端。它旨在提供一个功能齐全的 SIP 电话客户端，支持语音和视频通话。该客户端被设计为可在 Unity 环境中轻松集成和扩展，适用于需要实时通信功能的游戏或应用程序。

## 2. 系统架构

系统架构遵循模块化设计，将信令处理、媒体管理和用户界面分离。

- **SIP 信令层 (`SIPSignalingManager`)**: 负责处理所有与 SIP 协议相关的通信，包括注册、呼叫建立、呼叫终止等。
- **媒体处理层 (`MediaManager`, `AudioManager`, `VideoManager`)**: 管理音视频流的捕获、编码、解码和播放。
- **UI/场景集成层 (`SIPClientSceneSetup`)**: 在 Unity 场景中初始化和协调各个组件。
- **核心逻辑层 (`SIPClient`)**: 作为中心协调器，连接信令、媒体和UI。
- **工具类 (`UnityMainThreadDispatcher`)**: 提供在 Unity ���线程上执行代码的能力，以解决多线程问题。

## 3. 核心组件

### `SIPClient.cs`

- **职责**:
  - 作为 SIP 客户端的主要入口点和中心协调器。
  - 管理 `SIPSignalingManager` 和 `MediaManager` 的生命周期。
  - 提供简单的 API 用于发起和接听电话。
  - 处理来自 `SIPSignalingManager` 的核心事件（如来电、呼叫状态变更）。

### `SIPSignalingManager.cs`

- **职责**:
  - 使用 `SIPSorcery` 库处理 SIP 信令。
  - 管理 SIP 用户代理 (`SIPUserAgent`)。
  - 处理 SIP 注册 (`REGISTER`)、呼叫 (`INVITE`)、挂断 (`BYE`) 等请求。
  - 触发与呼叫状态相关的事件，供 `SIPClient` 消费。

### `MediaManager.cs`

- **职责**:
  - 协调 `AudioManager` 和 `VideoManager`。
  - 根据 `SIPSignalingManager` 的 SDP 协商结果，配置音视频流。
  - 创建和管理 `RTP` 会话。

### `AudioManager.cs`

- **职责**:
  - 从麦克风捕获音频数据。
  - 将捕获的音频数据编码并发送到 `MediaManager`。
  - 从 `MediaManager` 接收音频数据，解码并播放。
  - 管理 `AudioSource` 和 `AudioClip`。

### `VideoManager.cs`

- **职责**:
  - 捕获摄像头视频数据（如果支持）。
  - 将视频数据显示在 Unity UI 元素上（例如 `RawImage`）。
  - 使用 `RawImageVideoSink.cs` 作为视频帧的渲染目标。

### `SIPClientSceneSetup.cs`

- **职责**:
  - 在 Unity 场景启动时，实例化和配置 `SIPClient` 及其依赖项。
  - 通常附加到一个场景中的 GameObject 上，作为系统的启动器。
  - 通过 Inspector 公开配置参数（如 SIP 服务器地址、用户名、密码）。

### `UnityMainThreadDispatcher.cs`

- **职责**:
  - 提供一个静态方法，允许从任何线程（特别是网络线程）调度代码到 Unity 的主线程上执行。
  - 这对于更新 UI 或调用非线程安全的 Unity API至关重要。

## 4. 媒体处理

`Assets/Scripts/Media/` 目录包含用于处理音视频的辅助类。

- **`AudioPlayer.cs`**: 负责解码和播放传入的音频流。
- **`AudioRecorder.cs`**: 负责从麦克风录制音频。
- **`RawImageVideoSink.cs`**: 实现了 `IVideoSink` 接口，将解码后的视频帧渲染到 Unity 的 `RawImage` 组件上。

## 5. 测试

`Assets/Scripts/Tests/` 目录包含项目的单元测试。

- **`AudioManagerTests.cs`**: 测试 `AudioManager` 的功能。
- **`CircularBufferTests.cs`**: 测试环形缓冲区实现，这通常用于处理网络抖动。
- **`SIPClientTests.cs`**: 测试 `SIPClient` 的核心逻辑。

## 6. 设置与配置

1.  在 Unity 中打开主场景。
2.  找到挂载了 `SIPClientSceneSetup.cs` 脚本的 GameObject。
3.  在 Inspector 面板中，配置以下 SIP 账户信息：
    - SIP Server
    - SIP Username
    - SIP Password
4.  运行场景。客户端将自动注册到 SIP 服务器。

## 7. 依赖项

- **SIPSorcery**: 核心的 SIP 和 RTP 库。
- **Concentus**: Opus 编解码器实现。
- **FFmpeg.AutoGen**: 用于 FFmpeg 库的互操作。
