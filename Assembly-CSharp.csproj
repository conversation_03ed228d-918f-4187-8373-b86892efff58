﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <CscToolPath>D:\Unity\2020.3.48f1c1\Editor\Data\Tools\RoslynScripts</CscToolPath>
    <CscToolExe>unity_csc.bat</CscToolExe>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{3519E1DC-4E01-D1D4-475C-DA3C76B9E850}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2020_3_48;UNITY_2020_3;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;UNITY_UGP_API;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_IG;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_CLOUD_FEATURES;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
     <Compile Include="Assets\Scripts\Media\FFmpegVideoEncoderManager.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ShaderPropAnimator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SkewTextExample.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\WarpTextExample.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\SimpleScript.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_DigitValidator.cs" />
     <Compile Include="Assets\Scripts\UnityMainThreadDispatcher.cs" />
     <Compile Include="Assets\Scripts\Media\AudioExtrasSink.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark02.cs" />
     <Compile Include="Assets\Scripts\Media\AudioExtrasSource.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshSpawner.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01_UGUI.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_A.cs" />
     <Compile Include="Assets\Scripts\SIPClient.cs" />
     <Compile Include="Assets\Scripts\SIPClientSceneSetup.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark03.cs" />
     <Compile Include="Assets\Scripts\AudioManager.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeA.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark04.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_FrameRateCounter.cs" />
     <Compile Include="Assets\Scripts\SIPSignalingManager.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ChatController.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexJitter.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextMeshProFloatingText.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventHandler.cs" />
     <Compile Include="Assets\Scripts\Media\VideoExtrasSource.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexShakeB.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextSelector_B.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\EnvMapAnimator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_UiFrameRateCounter.cs" />
     <Compile Include="Assets\Scripts\VideoManager.cs" />
     <Compile Include="Assets\Scripts\Media\VideoExtrasSink.cs" />
     <Compile Include="Assets\Scripts\Media\FFmpegResourceManager.cs" />
     <Compile Include="Assets\Scripts\Media\CodecNegotiationManager.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\CameraController.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\DropdownSample.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_PhoneNumberValidator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMPro_InstructionOverlay.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexColorCycler.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TeleType.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_ExampleScript_01.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TextConsoleSimulator.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\VertexZoom.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\ObjectSpin.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextInfoDebugTool.cs" />
     <Compile Include="Assets\Scripts\Media\FFmpegVideoDecoder.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\Benchmark01.cs" />
     <Compile Include="Assets\TextMesh Pro\Examples &amp; Extras\Scripts\TMP_TextEventCheck.cs" />
     <Compile Include="Assets\Scripts\MediaManager.cs" />
     <Compile Include="Assets\Scripts\test\AudioDebugTool.cs" />
     <None Include="Assets\Packages\System.Buffers.4.5.1\version.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
     <None Include="Assets\Packages\System.Buffers.4.5.1\lib\netstandard2.0\System.Buffers.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
     <None Include="Assets\Packages\System.Security.AccessControl.5.0.0\lib\netstandard2.0\System.Security.AccessControl.xml" />
     <None Include="Assets\Packages\System.Security.Principal.Windows.5.0.0\lib\netstandard2.0\System.Security.Principal.Windows.xml" />
     <None Include="Assets\Packages\Portable.BouncyCastle.1.9.0\lib\netstandard2.0\BouncyCastle.Crypto.xml" />
     <None Include="Assets\Packages\SIPSorceryMedia.Abstractions.8.0.10\lib\netstandard2.0\SIPSorceryMedia.Abstractions.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
     <None Include="Assets\Packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\Packages\System.Buffers.4.5.1\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Oswald-Bold - OFL.txt" />
     <None Include="Assets\Packages\System.Threading.Tasks.Extensions.4.5.4\version.txt" />
     <None Include="Assets\Packages\System.Security.Principal.Windows.5.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Anton OFL.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
     <None Include="Assets\Packages\System.Diagnostics.DiagnosticSource.9.0.0\Sources\ILLink.Descriptors.LibraryBuild.xml" />
     <None Include="Assets\Packages\System.Memory.4.5.5\version.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
     <None Include="Assets\Packages\SIPSorceryMedia.FFmpeg.8.0.10\lib\netstandard2.0\SIPSorceryMedia.FFmpeg.xml" />
     <None Include="Assets\Packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
     <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\Packages\DirectShowLib.Standard.2.1.0\lib\netstandard2.0\DirectShowLib.xml" />
     <None Include="Assets\Packages\SIPSorcery.8.0.14\lib\netstandard2.0\SIPSorcery.xml" />
     <None Include="Assets\Packages\System.Security.AccessControl.5.0.0\version.txt" />
     <None Include="Assets\Packages\FFmpeg.AutoGen.7.0.0\lib\netstandard2.0\FFmpeg.AutoGen.xml" />
     <None Include="Assets\Packages\Microsoft.Win32.Registry.5.0.0\version.txt" />
     <None Include="Assets\Packages\Microsoft.Win32.Registry.5.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
     <None Include="Assets\Packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.xml" />
     <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
     <None Include="Assets\Packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
     <None Include="Assets\Packages\Microsoft.Win32.Registry.5.0.0\lib\netstandard2.0\Microsoft.Win32.Registry.xml" />
     <None Include="Assets\Packages\System.Threading.Tasks.Extensions.4.5.4\lib\netstandard2.0\System.Threading.Tasks.Extensions.xml" />
     <None Include="Assets\Packages\System.Security.AccessControl.5.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\Packages\System.Security.Principal.Windows.5.0.0\version.txt" />
     <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
     <None Include="Assets\Packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\netstandard2.0\Microsoft.Bcl.AsyncInterfaces.xml" />
     <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
     <None Include="Assets\Packages\System.Diagnostics.DiagnosticSource.9.0.0\useSharedDesignerContext.txt" />
     <None Include="Assets\Packages\System.Diagnostics.DiagnosticSource.9.0.0\content\ILLink\ILLink.Descriptors.LibraryBuild.xml" />
     <None Include="Assets\Packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\netstandard2.0\System.Diagnostics.DiagnosticSource.xml" />
     <None Include="Assets\Packages\DnsClient.1.8.0\lib\netstandard2.0\DnsClient.xml" />
     <None Include="Assets\Packages\System.Memory.4.5.5\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
     <None Include="Assets\Packages\FFmpeg.AutoGen.7.0.0\LICENSE.txt" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
     <None Include="Assets\Packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
     <None Include="Assets\Packages\System.Threading.Tasks.Extensions.4.5.4\useSharedDesignerContext.txt" />
     <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
     <None Include="Assets\Packages\Concentus.2.2.2\lib\netstandard2.0\Concentus.xml" />
     <None Include="Assets\Packages\System.Memory.4.5.5\lib\netstandard2.0\System.Memory.xml" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
     <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
     <None Include="Assets\TextMesh Pro\Examples &amp; Extras\Fonts\Bangers - OFL.txt" />
 <Reference Include="UnityEngine">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AIModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ARModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AccessibilityModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AndroidJNIModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AnimationModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AssetBundleModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AudioModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AutoStreamingModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AutoStreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClothModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CloudFoundationModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CloudFoundationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterInputModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterRendererModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CoreModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CrashReportingModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DSPGraphModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DirectorModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GIModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GameCenterModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GridModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.HotReloadModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.IMGUIModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ImageConversionModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputLegacyModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.JSONSerializeModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.LocalizationModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ParticleSystemModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PerformanceReportingModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PhysicsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Physics2DModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ProfilerModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ScreenCaptureModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SharedInternalsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteMaskModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteShapeModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StreamingModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubstanceModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubsystemsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TLSModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainPhysicsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextCoreModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextRenderingModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TilemapModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsNativeModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIWidgetsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIWidgetsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UNETModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UmbraModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityConnectModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityCurlModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityTestProtocolModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAudioModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestTextureModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestWWWModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VFXModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VRModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VehiclesModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VideoModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VirtualTexturingModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WindModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XRModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.CoreModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.GraphViewModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.PackageManagerUIModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.SceneTemplateModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UIElementsModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UIElementsSamplesModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UIServiceModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UnityConnectModule">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="System.Buffers">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Buffers.4.5.1/lib/netstandard2.0/System.Buffers.dll</HintPath>
 </Reference>
 <Reference Include="SIPSorcery">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/SIPSorcery.8.0.14/lib/netstandard2.0/SIPSorcery.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.Unsafe">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
 </Reference>
 <Reference Include="SIPSorceryMedia.Abstractions">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/SIPSorceryMedia.Abstractions.8.0.10/lib/netstandard2.0/SIPSorceryMedia.Abstractions.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal.Windows">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Security.Principal.Windows.5.0.0/lib/netstandard2.0/System.Security.Principal.Windows.dll</HintPath>
 </Reference>
 <Reference Include="DirectShowLib">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/DirectShowLib.Standard.2.1.0/lib/netstandard2.0/DirectShowLib.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.DiagnosticSource">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Diagnostics.DiagnosticSource.9.0.0/lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Bcl.AsyncInterfaces">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.0/lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
 </Reference>
 <Reference Include="BouncyCastle.Crypto">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/Portable.BouncyCastle.1.9.0/lib/netstandard2.0/BouncyCastle.Crypto.dll</HintPath>
 </Reference>
 <Reference Include="System.Memory">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Memory.4.5.5/lib/netstandard2.0/System.Memory.dll</HintPath>
 </Reference>
 <Reference Include="Newtonsoft.Json">
 <HintPath>D:/Unity/司导/SIP2.0/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll</HintPath>
 </Reference>
 <Reference Include="Concentus">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/Concentus.2.2.2/lib/netstandard2.0/Concentus.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.AccessControl">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Security.AccessControl.5.0.0/lib/netstandard2.0/System.Security.AccessControl.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0/lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
 </Reference>
 <Reference Include="websocket-sharp">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/SIPSorcery.WebSocketSharp.0.0.1/lib/netstandard2.0/websocket-sharp.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Extensions.Logging.Abstractions">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/Microsoft.Extensions.Logging.Abstractions.9.0.0/lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Registry">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/Microsoft.Win32.Registry.5.0.0/lib/netstandard2.0/Microsoft.Win32.Registry.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Extensions">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/System.Threading.Tasks.Extensions.4.5.4/lib/netstandard2.0/System.Threading.Tasks.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="SIPSorceryMedia.FFmpeg">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/SIPSorceryMedia.FFmpeg.8.0.10/lib/netstandard2.0/SIPSorceryMedia.FFmpeg.dll</HintPath>
 </Reference>
 <Reference Include="FFmpeg.AutoGen">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/FFmpeg.AutoGen.7.0.0/lib/netstandard2.0/FFmpeg.AutoGen.dll</HintPath>
 </Reference>
 <Reference Include="DnsClient">
 <HintPath>D:/Unity/司导/SIP2.0/Assets/Packages/DnsClient.1.8.0/lib/netstandard2.0/DnsClient.dll</HintPath>
 </Reference>
 <Reference Include="mscorlib">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
 </Reference>
 <Reference Include="System">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
 </Reference>
 <Reference Include="System.Core">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.Linq">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.CSharp">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
 </Reference>
 <Reference Include="System.Data">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="netstandard">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
 </Reference>
 <Reference Include="System.AppContext">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Concurrent">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.NonGeneric">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Specialized">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Annotations">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.EventBasedAsync">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.TypeConverter">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
 </Reference>
 <Reference Include="System.Console">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
 </Reference>
 <Reference Include="System.Data.Common">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Contracts">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Debug">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.FileVersionInfo">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Process">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.StackTrace">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TextWriterTraceListener">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Tools">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TraceSource">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
 </Reference>
 <Reference Include="System.Drawing.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Dynamic.Runtime">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Calendars">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Extensions">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression.ZipFile">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
 </Reference>
 <Reference Include="System.IO">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.DriveInfo">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Watcher">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.IsolatedStorage">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.MemoryMappedFiles">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Pipes">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.UnmanagedMemoryStream">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Expressions">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Parallel">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Queryable">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http.Rtc">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NameResolution">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NetworkInformation">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Ping">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Requests">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Security">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Sockets">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebHeaderCollection">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets.Client">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
 </Reference>
 <Reference Include="System.ObjectModel">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.ILGeneration">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.Lightweight">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Extensions">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Reader">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.ResourceManager">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Writer">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.VisualC">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Extensions">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Handles">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Numerics">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Formatters">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Json">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Xml">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Claims">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Algorithms">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Csp">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Encoding">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.X509Certificates">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.SecureString">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Duplex">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Http">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.NetTcp">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Primitives">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Security">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding.Extensions">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.RegularExpressions">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Overlapped">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Parallel">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Thread">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.ThreadPool">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Timer">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
 </Reference>
 <Reference Include="System.ValueTuple">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.ReaderWriter">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XDocument">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlDocument">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlSerializer">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath.XDocument">
 <HintPath>D:/Unity/2020.3.48f1c1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="Unity.VSCode.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.VSCode.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.VisualStudio.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Sysroot.Linux_x86_64">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.Sysroot.Linux_x86_64.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Toolchain.Win-x86_64-Linux-x86_64">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.Toolchain.Win-x86_64-Linux-x86_64.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Timeline.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Timeline">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UI">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
 </Reference>
 <Reference Include="Unity.PlasticSCM.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Rider.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.Rider.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UI">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
 </Reference>
 <Reference Include="Unity.SysrootPackage.Editor">
 <HintPath>D:/Unity/司导/SIP2.0/Library/ScriptAssemblies/Unity.SysrootPackage.Editor.dll</HintPath>
 </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
