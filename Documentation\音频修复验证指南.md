# Unity SIP 音频修复验证指南

## 📋 验证概述

本指南将帮助您验证Unity SIP项目中音频播放修复的效果。由于AudioManager和AudioExtrasSink是通过SIPClientSceneSetup动态创建的，我们需要按照特定的步骤进行验证。

---

## 🏗️ 架构理解

### 组件创建流程
```
SIPClientSceneSetup.Start()
    ↓
SetupSIPClient()
    ↓
sipManager.AddComponent<AudioManager>()        // 第670行
sipManager.AddComponent<VideoManager>()        // 第671行  
sipManager.AddComponent<MediaManager>()        // 第674行
    ↓
MediaManager 自动创建 AudioExtrasSink         // 在MediaManager初始化时
```

### 关键对象
- **SIPManager GameObject**：包含所有SIP相关组件的容器
- **AudioManager**：音频管理器，动态添加到SIPManager
- **AudioExtrasSink**：音频接收器，由MediaManager自动创建
- **AudioDebugTool**：我们的调试工具，需要手动添加到场景

---

## 🚀 验证步骤

### 第1步：添加调试工具
1. 在Unity场景中创建一个空的GameObject
2. 将 `Assets/Scripts/test/AudioDebugTool.cs` 脚本添加到该GameObject
3. 在Inspector中配置AudioDebugTool参数：
   - ✅ `Enable Debug Logs`: true
   - ✅ `Auto Check Environment`: true
   - ✅ `Show GUI`: true

### 第2步：启动项目并观察日志
启动Unity项目，观察Console中的日志输出：

**预期的正常日志**：
```
[AudioDebugTool] 音频调试工具已启动
  - 按 F1 检查音频环境
  - 按 F2 生成测试音频
  - 按 F3 显示统计信息
  - 按 F4 切换GUI显示

[AudioDebugTool] 组件查找结果:
  - AudioExtrasSink: ✅ 已找到
  - AudioManager: ✅ 已找到

[AudioDebugTool] 找到SIPManager对象: SIPManager
[AudioDebugTool] SIPManager上的组件:
    - Transform
    - SIPClient
    - AudioManager
    - VideoManager
    - MediaManager
    - AudioExtrasSink

[AudioDebugTool] 自动检查音频环境...
```

### 第3步：检查音频环境配置
按 **F1** 键或点击GUI中的"检查音频环境"按钮，观察输出：

**预期的环境检查日志**：
```
=== Unity音频环境检查 ===
Unity音频配置:
  - 输出采样率: 48000Hz
  - DSP缓冲区: 1024样本
  - 扬声器模式: Stereo

AudioSource状态:
  - 是否播放: True
  - 音量: 1.00
  - 优先级: 64
  - Clip采样率: 48000Hz
  - Clip长度: 1.00秒
  - Clip声道: 1

G.722解码器:
  - 解码器已初始化: True
  - 输入采样率: 8000Hz (RTP时钟)
  - 输出采样率: 16000Hz (PCM)
  - 播放采样率: 48000Hz
  - 需要重采样: True

音频统计:
  - 接收包数: 0
  - 解码帧数: 0
  - 播放帧数: 0
  - 静音帧数: 0
  - 队列大小: 0
  - 最大音频电平: 0.000
  - 平均音频电平: 0.000
=== 检查完成 ===
```

### 第4步：测试音频播放功能
按 **F2** 键或点击GUI中的"生成测试音频"按钮：

**预期结果**：
- 应该能听到1kHz的正弦波测试音频（持续约20ms）
- Console显示：`[AudioExtrasSink] 测试音频已加入队列，样本数: XXX`

### 第5步：建立SIP连接并测试
1. 建立SIP连接
2. 进行实际通话测试
3. 观察音频统计信息的变化

**通话时的预期日志**：
```
[AudioExtrasSink] 接收到第一个RTP包: Seq=12345, PayloadType=9, Size=160字节
[AudioExtrasSink] 音频处理统计: Seq=12400, 解码=320样本, 队列=3, 重采样=True

[AudioExtrasSink] 音频状态监控:
  - AudioSource播放: True
  - 距离上次音频: 0.1秒
  - 当前队列大小: 5
  - 已播放音频: True
  - 统计信息: 接收=150, 解码=150, 播放=145, 静音=5
```

---

## 🔧 GUI界面说明

### 实时状态显示
AudioDebugTool的GUI界面会显示：

- **🎛️ Unity采样率**: 当前系统音频采样率
- **🔗 音频会话**: 
  - ✅ 已启动 (绿色) - 音频会话正常
  - ❌ 未启动 (红色) - 音频会话异常
- **🔊 SIP AudioSource**: 场景中SIP相关的AudioSource数量
- **📻 播放状态**:
  - ▶️ 播放中 (绿色) - AudioSource正在播放
  - ⏹️ 已停止 (黄色) - AudioSource已停止
- **🔊 音量**: 当前AudioSource音量值

### 快捷键功能
- **F1** 🔍：检查音频环境配置
- **F2** 🎵：生成1kHz测试音频信号
- **F3** 📊：显示详细统计信息
- **F4** 🔄：切换GUI显示开关
- **F5** 🔧：准备音频会话（手动创建AudioExtrasSink）

---

## ❌ 常见问题排查

### 问题1：找不到AudioExtrasSink组件
**症状**：
```
[AudioDebugTool] 组件查找结果:
  - AudioExtrasSink: ❌ 未找到
  - AudioManager: ✅ 已找到
```

**原因分析**：
AudioExtrasSink不是在启动时自动创建的，而是在调用`AudioManager.PrepareAudioSession()`时才创建。

**解决方案**：
1. **按F5键**或点击GUI中的"🔧 准备音频会话 (F5)"按钮
2. 观察Console输出：
   ```
   [AudioDebugTool] 手动准备音频会话...
   [AudioDebugTool] 音频会话准备成功
   [AudioDebugTool] ✅ AudioExtrasSink已成功创建
   ```
3. 如果仍然失败，检查AudioManager的初始化状态

### 问题2：AudioSource未播放
**症状**：
```
AudioSource状态:
  - 是否播放: False
```

**解决方案**：
1. 检查Unity音频设备是否正常
2. 确认系统音量未静音
3. 按F2测试音频生成功能

### 问题3：采样率不匹配
**症状**：
```
G.722解码器:
  - 需要重采样: True
  - 播放采样率: 48000Hz
```

**解决方案**：
- 这是正常现象，我们的修复已经实现了自动重采样
- 确认日志中显示"启用重采样: 16000Hz → 48000Hz"

### 问题4：音频电平过低
**症状**：
```
[AudioExtrasSink] 音频电平过低: Seq=12345, 平均电平=0.000001
```

**解决方案**：
1. 检查麦克风音量设置
2. 确认对方是否在说话
3. 调整AudioSource音量

---

## 📊 成功验证的标志

### ✅ 完全成功的验证应该显示：

1. **组件创建成功**：
   - AudioExtrasSink: ✅ 已找到
   - AudioManager: ✅ 已找到

2. **音频配置正确**：
   - AudioSource播放状态: True
   - 采样率适配: 需要重采样: True
   - 解码器初始化: True

3. **测试音频正常**：
   - 按F2能听到1kHz测试音频
   - 测试音频加入队列成功

4. **实际通话音频**：
   - 能接收到RTP包
   - 音频解码成功
   - 能听到对方声音

### 🎯 关键性能指标：
- **端到端延迟**: < 150ms
- **丢包率**: < 1%
- **音频质量**: 清晰无杂音
- **CPU占用**: < 10%（音频处理部分）

---

## 📞 技术支持

如果验证过程中遇到问题，请提供：

1. **完整的Console日志**（特别是AudioDebugTool的输出）
2. **F1检查环境的完整输出**
3. **系统信息**：Unity版本、操作系统、音频设备
4. **具体问题描述**：什么时候出现、如何复现

---

*验证指南版本：1.0*  
*最后更新：2025年7月15日*  
*适用于：Unity SIP 2.0 音频播放修复版本*
