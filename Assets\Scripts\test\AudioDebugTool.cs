using UnityEngine;
using SIPSorcery.Media;

/// <summary>
/// 音频调试工具 - 用于测试和验证音频播放修复
/// </summary>
public class AudioDebugTool : MonoBehaviour
{
    [Header("调试控制")]
    public bool enableDebugLogs = true;
    public bool autoCheckEnvironment = true;
    
    [Header("测试控制")]
    public KeyCode checkEnvironmentKey = KeyCode.F1;
    public KeyCode generateTestAudioKey = KeyCode.F2;
    public KeyCode showStatsKey = KeyCode.F3;
    public KeyCode toggleGUIKey = KeyCode.F4;
    public KeyCode prepareAudioSessionKey = KeyCode.F5;
    public KeyCode diagnoseAudioSourceKey = KeyCode.F6;

    [Header("GUI设置")]
    public bool showGUI = true;
    public float guiUpdateInterval = 0.5f;

    private AudioExtrasSink _audioSink;
    private AudioManager _audioManager;
    private float _lastGUIUpdate = 0f;
    
    void Start()
    {
        // 由于AudioManager和AudioExtrasSink是通过SIPClientSceneSetup动态创建的
        // 我们需要延迟查找这些组件
        StartCoroutine(FindAudioComponentsDelayed());
    }

    /// <summary>
    /// 延迟查找音频组件，因为它们是动态创建的
    /// </summary>
    private System.Collections.IEnumerator FindAudioComponentsDelayed()
    {
        // 等待几帧，确保SIPClientSceneSetup完成组件创建
        yield return new WaitForSeconds(1.0f);

        // 查找音频组件
        _audioSink = FindObjectOfType<AudioExtrasSink>();
        _audioManager = FindObjectOfType<AudioManager>();

        if (_audioSink == null)
        {
            Debug.LogWarning("[AudioDebugTool] 未找到AudioExtrasSink组件 - 尝试触发音频会话准备");

            // 尝试触发音频会话准备，这会创建AudioExtrasSink
            if (_audioManager != null)
            {
                try
                {
                    var mediaSession = _audioManager.PrepareAudioSession();
                    if (mediaSession != null)
                    {
                        Debug.Log("[AudioDebugTool] 音频会话准备成功，重新查找AudioExtrasSink");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"[AudioDebugTool] 准备音频会话失败: {ex.Message}");
                }
            }

            // 再等待一段时间重试
            yield return new WaitForSeconds(2.0f);
            _audioSink = FindObjectOfType<AudioExtrasSink>();

            if (_audioSink == null)
            {
                Debug.LogError("[AudioDebugTool] 仍然未找到AudioExtrasSink组件，可能存在初始化问题");
            }
        }

        if (_audioManager == null)
        {
            Debug.LogWarning("[AudioDebugTool] 未找到AudioManager组件 - 可能还未被SIPClientSceneSetup创建");
        }

        // 显示查找结果和详细信息
        Debug.Log($"[AudioDebugTool] 组件查找结果:");
        Debug.Log($"  - AudioExtrasSink: {(_audioSink != null ? "✅ 已找到" : "❌ 未找到")}");
        Debug.Log($"  - AudioManager: {(_audioManager != null ? "✅ 已找到" : "❌ 未找到")}");

        // 查找SIPManager对象以获取更多信息
        var sipManager = GameObject.Find("SIPManager");
        if (sipManager != null)
        {
            Debug.Log($"[AudioDebugTool] 找到SIPManager对象: {sipManager.name}");
            var components = sipManager.GetComponents<Component>();
            Debug.Log($"[AudioDebugTool] SIPManager上的组件:");
            foreach (var comp in components)
            {
                Debug.Log($"    - {comp.GetType().Name}");
            }
        }
        else
        {
            Debug.LogWarning("[AudioDebugTool] 未找到SIPManager对象");
        }

        // 自动检查环境
        if (autoCheckEnvironment)
        {
            yield return new WaitForSeconds(1.0f); // 再等待1秒确保完全初始化
            CheckEnvironmentDelayed();
        }
        
        // 显示使用说明
        Debug.Log("[AudioDebugTool] 音频调试工具已启动");
        Debug.Log($"  - 按 {checkEnvironmentKey} 检查音频环境");
        Debug.Log($"  - 按 {generateTestAudioKey} 生成测试音频");
        Debug.Log($"  - 按 {showStatsKey} 显示统计信息");
        Debug.Log($"  - 按 {toggleGUIKey} 切换GUI显示");
        Debug.Log($"  - 按 {prepareAudioSessionKey} 准备音频会话（创建AudioExtrasSink）");
        Debug.Log($"  - 按 {diagnoseAudioSourceKey} 深度诊断AudioSource问题");
    }
    
    void Update()
    {
        if (_audioSink == null) return;
        
        // 键盘快捷键
        if (Input.GetKeyDown(checkEnvironmentKey))
        {
            CheckAudioEnvironment();
        }
        
        if (Input.GetKeyDown(generateTestAudioKey))
        {
            GenerateTestAudio();
        }
        
        if (Input.GetKeyDown(showStatsKey))
        {
            ShowAudioStats();
        }

        if (Input.GetKeyDown(toggleGUIKey))
        {
            showGUI = !showGUI;
            Debug.Log($"[AudioDebugTool] GUI显示: {(showGUI ? "开启" : "关闭")}");
        }

        if (Input.GetKeyDown(prepareAudioSessionKey))
        {
            PrepareAudioSession();
        }

        if (Input.GetKeyDown(diagnoseAudioSourceKey))
        {
            DiagnoseAudioSource();
        }
    }
    
    /// <summary>
    /// 延迟检查音频环境
    /// </summary>
    private void CheckEnvironmentDelayed()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 自动检查音频环境...");
            _audioSink.CheckAudioEnvironment();
        }
    }
    
    /// <summary>
    /// 检查音频环境
    /// </summary>
    public void CheckAudioEnvironment()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 手动检查音频环境...");
            _audioSink.CheckAudioEnvironment();
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法检查环境");
        }
    }
    
    /// <summary>
    /// 准备音频会话（手动触发AudioExtrasSink创建）
    /// </summary>
    public void PrepareAudioSession()
    {
        if (_audioManager != null)
        {
            try
            {
                Debug.Log("[AudioDebugTool] 手动准备音频会话...");
                var mediaSession = _audioManager.PrepareAudioSession();

                if (mediaSession != null)
                {
                    Debug.Log("[AudioDebugTool] 音频会话准备成功");

                    // 重新查找AudioExtrasSink
                    _audioSink = FindObjectOfType<AudioExtrasSink>();
                    if (_audioSink != null)
                    {
                        Debug.Log("[AudioDebugTool] ✅ AudioExtrasSink已成功创建");
                    }
                    else
                    {
                        Debug.LogWarning("[AudioDebugTool] AudioExtrasSink仍未找到");
                    }
                }
                else
                {
                    Debug.LogError("[AudioDebugTool] 音频会话准备失败");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AudioDebugTool] 准备音频会话异常: {ex.Message}");
            }
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioManager未找到，无法准备音频会话");
        }
    }

    /// <summary>
    /// 生成测试音频
    /// </summary>
    public void GenerateTestAudio()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 生成测试音频信号...");

            // 先检查AudioSource状态
            var audioSources = FindObjectsOfType<AudioSource>();
            var sipAudioSource = System.Array.Find(audioSources,
                s => s.gameObject.name.Contains("SIP") || (s.clip?.name.Contains("SIP") == true));

            if (sipAudioSource != null)
            {
                Debug.Log($"[AudioDebugTool] 测试前AudioSource状态:");
                Debug.Log($"  - 是否播放: {sipAudioSource.isPlaying}");
                Debug.Log($"  - 音量: {sipAudioSource.volume}");
                Debug.Log($"  - 是否静音: {sipAudioSource.mute}");
                Debug.Log($"  - AudioClip: {(sipAudioSource.clip != null ? sipAudioSource.clip.name : "null")}");

                // 确保AudioSource正在播放
                if (!sipAudioSource.isPlaying)
                {
                    Debug.LogWarning("[AudioDebugTool] AudioSource未播放，尝试启动...");
                    sipAudioSource.Play();
                    Debug.Log($"[AudioDebugTool] 启动后播放状态: {sipAudioSource.isPlaying}");
                }
            }
            else
            {
                Debug.LogError("[AudioDebugTool] 未找到SIP相关的AudioSource");
            }

            // 生成测试音频
            _audioSink.GenerateTestAudio();

            // 等待一小段时间后检查结果
            StartCoroutine(CheckTestAudioResult());
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法生成测试音频");
        }
    }

    /// <summary>
    /// 检查测试音频结果
    /// </summary>
    private System.Collections.IEnumerator CheckTestAudioResult()
    {
        yield return new WaitForSeconds(1.0f); // 等待1秒

        Debug.Log("[AudioDebugTool] 测试音频结果检查:");
        if (_audioSink != null)
        {
            // 调用环境检查来显示统计信息
            _audioSink.CheckAudioEnvironment();
        }
    }

    /// <summary>
    /// 深度诊断AudioSource问题
    /// </summary>
    public void DiagnoseAudioSource()
    {
        Debug.Log("=== AudioSource深度诊断 ===");

        // 查找所有AudioSource
        var audioSources = FindObjectsOfType<AudioSource>();
        Debug.Log($"场景中总共有 {audioSources.Length} 个AudioSource");

        AudioSource sipAudioSource = null;

        foreach (var source in audioSources)
        {
            bool isSipRelated = source.gameObject.name.Contains("SIP") ||
                               (source.clip?.name.Contains("SIP") == true) ||
                               source.gameObject.GetComponent<AudioExtrasSink>() != null;

            Debug.Log($"AudioSource: {source.gameObject.name}");
            Debug.Log($"  - SIP相关: {isSipRelated}");
            Debug.Log($"  - 播放状态: {source.isPlaying}");
            Debug.Log($"  - 音量: {source.volume}");
            Debug.Log($"  - 静音: {source.mute}");
            Debug.Log($"  - 优先级: {source.priority}");
            Debug.Log($"  - 空间混合: {source.spatialBlend}");
            Debug.Log($"  - 循环: {source.loop}");

            if (source.clip != null)
            {
                Debug.Log($"  - AudioClip: {source.clip.name}");
                Debug.Log($"  - Clip采样率: {source.clip.frequency}Hz");
                Debug.Log($"  - Clip长度: {source.clip.length:F2}秒");
                Debug.Log($"  - Clip样本数: {source.clip.samples}");
                Debug.Log($"  - Clip声道: {source.clip.channels}");
                Debug.Log($"  - Clip加载状态: {source.clip.loadState}");
            }
            else
            {
                Debug.Log($"  - AudioClip: null");
            }

            if (isSipRelated)
            {
                sipAudioSource = source;
            }
        }

        // 专门诊断SIP AudioSource
        if (sipAudioSource != null)
        {
            Debug.Log("=== SIP AudioSource专项诊断 ===");

            // 检查AudioListener
            var audioListener = FindObjectOfType<AudioListener>();
            if (audioListener != null)
            {
                Debug.Log($"AudioListener: {audioListener.gameObject.name}");
                Debug.Log($"  - 启用状态: {audioListener.enabled}");
                Debug.Log($"  - 音量: {AudioListener.volume}");
                Debug.Log($"  - 暂停状态: {AudioListener.pause}");
            }
            else
            {
                Debug.LogError("未找到AudioListener！这会导致无法听到音频");
            }

            // 检查Unity音频设置
            Debug.Log("Unity音频系统状态:");
            Debug.Log($"  - 输出采样率: {AudioSettings.outputSampleRate}Hz");
            Debug.Log($"  - DSP缓冲区: {AudioSettings.GetConfiguration().dspBufferSize}");
            Debug.Log($"  - 扬声器模式: {AudioSettings.GetConfiguration().speakerMode}");

            // 尝试手动播放测试
            Debug.Log("尝试手动启动AudioSource播放...");
            if (!sipAudioSource.isPlaying)
            {
                sipAudioSource.Play();
                Debug.Log($"手动启动后播放状态: {sipAudioSource.isPlaying}");
            }

            // 检查OnAudioRead回调
            if (sipAudioSource.clip != null)
            {
                Debug.Log($"AudioClip类型: {sipAudioSource.clip.GetType().Name}");
                Debug.Log($"是否为程序生成的AudioClip: {sipAudioSource.clip.name == "SIPAudioPlayback"}");
            }
        }
        else
        {
            Debug.LogError("未找到SIP相关的AudioSource！");
        }

        Debug.Log("=== 诊断完成 ===");
    }
    
    /// <summary>
    /// 显示音频统计信息
    /// </summary>
    public void ShowAudioStats()
    {
        Debug.Log("[AudioDebugTool] 音频统计信息:");
        
        // Unity音频设置
        Debug.Log($"  Unity音频设置:");
        Debug.Log($"    - 输出采样率: {AudioSettings.outputSampleRate}Hz");
        Debug.Log($"    - DSP缓冲区: {AudioSettings.GetConfiguration().dspBufferSize}");
        Debug.Log($"    - 扬声器模式: {AudioSettings.GetConfiguration().speakerMode}");
        
        // AudioManager状态
        if (_audioManager != null)
        {
            Debug.Log($"  AudioManager状态:");
            Debug.Log($"    - 音频会话已启动: {_audioManager.IsAudioSessionStarted()}");
        }
        
        // AudioSource状态
        var audioSources = FindObjectsOfType<AudioSource>();
        Debug.Log($"  场景中的AudioSource数量: {audioSources.Length}");
        
        foreach (var source in audioSources)
        {
            if (source.gameObject.name.Contains("SIP") || source.clip?.name.Contains("SIP") == true)
            {
                Debug.Log($"    SIP AudioSource: {source.gameObject.name}");
                Debug.Log($"      - 播放状态: {source.isPlaying}");
                Debug.Log($"      - 音量: {source.volume}");
                Debug.Log($"      - 优先级: {source.priority}");
                if (source.clip != null)
                {
                    Debug.Log($"      - Clip: {source.clip.name}, {source.clip.frequency}Hz, {source.clip.length:F2}s");
                }
            }
        }
    }
    
    /// <summary>
    /// GUI显示调试信息
    /// </summary>
    void OnGUI()
    {
        if (!enableDebugLogs || !showGUI) return;

        // 限制GUI更新频率以提高性能
        if (Time.realtimeSinceStartup - _lastGUIUpdate < guiUpdateInterval) return;
        _lastGUIUpdate = Time.realtimeSinceStartup;

        // 设置GUI样式
        GUIStyle titleStyle = new GUIStyle(GUI.skin.label);
        titleStyle.fontSize = 16;
        titleStyle.fontStyle = FontStyle.Bold;
        titleStyle.normal.textColor = Color.white;

        GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
        buttonStyle.fontSize = 12;

        GUIStyle labelStyle = new GUIStyle(GUI.skin.label);
        labelStyle.fontSize = 11;
        labelStyle.normal.textColor = Color.cyan;

        GUILayout.BeginArea(new Rect(10, 10, 320, 250));
        GUILayout.BeginVertical("box");

        GUILayout.Label("🎵 音频调试工具", titleStyle);

        GUILayout.Space(10);
        
        if (GUILayout.Button("🔍 检查音频环境 (F1)", buttonStyle))
        {
            CheckAudioEnvironment();
        }

        if (GUILayout.Button("🎵 生成测试音频 (F2)", buttonStyle))
        {
            GenerateTestAudio();
        }

        if (GUILayout.Button("📊 显示统计信息 (F3)", buttonStyle))
        {
            ShowAudioStats();
        }

        if (GUILayout.Button("🔧 准备音频会话 (F5)", buttonStyle))
        {
            PrepareAudioSession();
        }

        if (GUILayout.Button("🔬 深度诊断AudioSource (F6)", buttonStyle))
        {
            DiagnoseAudioSource();
        }

        GUILayout.Space(10);

        // 显示基本状态
        GUILayout.Label($"🎛️ Unity采样率: {AudioSettings.outputSampleRate}Hz", labelStyle);

        if (_audioManager != null)
        {
            bool sessionStarted = _audioManager.IsAudioSessionStarted();
            string sessionStatus = sessionStarted ? "✅ 已启动" : "❌ 未启动";
            GUIStyle sessionStyle = new GUIStyle(labelStyle);
            sessionStyle.normal.textColor = sessionStarted ? Color.green : Color.red;
            GUILayout.Label($"🔗 音频会话: {sessionStatus}", sessionStyle);
        }

        var sipAudioSources = System.Array.FindAll(FindObjectsOfType<AudioSource>(),
            s => s.gameObject.name.Contains("SIP") || (s.clip?.name.Contains("SIP") == true));
        GUILayout.Label($"🔊 SIP AudioSource: {sipAudioSources.Length}个", labelStyle);

        if (sipAudioSources.Length > 0)
        {
            var source = sipAudioSources[0];
            bool isPlaying = source.isPlaying;
            string playStatus = isPlaying ? "▶️ 播放中" : "⏹️ 已停止";
            GUIStyle playStyle = new GUIStyle(labelStyle);
            playStyle.normal.textColor = isPlaying ? Color.green : Color.yellow;
            GUILayout.Label($"📻 播放状态: {playStatus}", playStyle);
            GUILayout.Label($"🔊 音量: {source.volume:F2}", labelStyle);
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
