using UnityEngine;
using SIPSorcery.Media;

/// <summary>
/// 音频调试工具 - 用于测试和验证音频播放修复
/// </summary>
public class AudioDebugTool : MonoBehaviour
{
    [Header("调试控制")]
    public bool enableDebugLogs = true;
    public bool autoCheckEnvironment = true;
    
    [Header("测试控制")]
    public KeyCode checkEnvironmentKey = KeyCode.F1;
    public KeyCode generateTestAudioKey = KeyCode.F2;
    public KeyCode showStatsKey = KeyCode.F3;
    public KeyCode toggleGUIKey = KeyCode.F4;
    public KeyCode prepareAudioSessionKey = KeyCode.F5;
    public KeyCode diagnoseAudioSourceKey = KeyCode.F6;
    public KeyCode generateLongTestAudioKey = KeyCode.F7;
    public KeyCode debugGenerateTestAudioKey = KeyCode.F8;
    public KeyCode diagnoseOnAudioReadKey = KeyCode.F9;
    public KeyCode forceRestartAudioSourceKey = KeyCode.F10;

    [Header("GUI设置")]
    public bool showGUI = true;
    public float guiUpdateInterval = 0.5f;

    private AudioExtrasSink _audioSink;
    private AudioManager _audioManager;
    private float _lastGUIUpdate = 0f;
    
    void Start()
    {
        // 由于AudioManager和AudioExtrasSink是通过SIPClientSceneSetup动态创建的
        // 我们需要延迟查找这些组件
        StartCoroutine(FindAudioComponentsDelayed());
    }

    /// <summary>
    /// 延迟查找音频组件，因为它们是动态创建的
    /// </summary>
    private System.Collections.IEnumerator FindAudioComponentsDelayed()
    {
        // 等待几帧，确保SIPClientSceneSetup完成组件创建
        yield return new WaitForSeconds(1.0f);

        // 查找音频组件
        _audioSink = FindObjectOfType<AudioExtrasSink>();
        _audioManager = FindObjectOfType<AudioManager>();

        if (_audioSink == null)
        {
            Debug.LogWarning("[AudioDebugTool] 未找到AudioExtrasSink组件 - 尝试触发音频会话准备");

            // 尝试触发音频会话准备，这会创建AudioExtrasSink
            if (_audioManager != null)
            {
                try
                {
                    var mediaSession = _audioManager.PrepareAudioSession();
                    if (mediaSession != null)
                    {
                        Debug.Log("[AudioDebugTool] 音频会话准备成功，重新查找AudioExtrasSink");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"[AudioDebugTool] 准备音频会话失败: {ex.Message}");
                }
            }

            // 再等待一段时间重试
            yield return new WaitForSeconds(2.0f);
            _audioSink = FindObjectOfType<AudioExtrasSink>();

            if (_audioSink == null)
            {
                Debug.LogError("[AudioDebugTool] 仍然未找到AudioExtrasSink组件，可能存在初始化问题");
            }
        }

        if (_audioManager == null)
        {
            Debug.LogWarning("[AudioDebugTool] 未找到AudioManager组件 - 可能还未被SIPClientSceneSetup创建");
        }

        // 显示查找结果和详细信息
        Debug.Log($"[AudioDebugTool] 组件查找结果:");
        Debug.Log($"  - AudioExtrasSink: {(_audioSink != null ? "✅ 已找到" : "❌ 未找到")}");
        Debug.Log($"  - AudioManager: {(_audioManager != null ? "✅ 已找到" : "❌ 未找到")}");

        // 查找SIPManager对象以获取更多信息
        var sipManager = GameObject.Find("SIPManager");
        if (sipManager != null)
        {
            Debug.Log($"[AudioDebugTool] 找到SIPManager对象: {sipManager.name}");
            var components = sipManager.GetComponents<Component>();
            Debug.Log($"[AudioDebugTool] SIPManager上的组件:");
            foreach (var comp in components)
            {
                Debug.Log($"    - {comp.GetType().Name}");
            }
        }
        else
        {
            Debug.LogWarning("[AudioDebugTool] 未找到SIPManager对象");
        }

        // 自动检查环境
        if (autoCheckEnvironment)
        {
            yield return new WaitForSeconds(1.0f); // 再等待1秒确保完全初始化
            CheckEnvironmentDelayed();
        }
        
        // 显示使用说明
        Debug.Log("[AudioDebugTool] 音频调试工具已启动");
        Debug.Log($"  - 按 {checkEnvironmentKey} 检查音频环境");
        Debug.Log($"  - 按 {generateTestAudioKey} 生成测试音频");
        Debug.Log($"  - 按 {showStatsKey} 显示统计信息");
        Debug.Log($"  - 按 {toggleGUIKey} 切换GUI显示");
        Debug.Log($"  - 按 {prepareAudioSessionKey} 准备音频会话（创建AudioExtrasSink）");
        Debug.Log($"  - 按 {diagnoseAudioSourceKey} 深度诊断AudioSource问题");
        Debug.Log($"  - 按 {generateLongTestAudioKey} 生成长时间测试音频（1秒）");
        Debug.Log($"  - 按 {debugGenerateTestAudioKey} 调试单个测试音频生成");
        Debug.Log($"  - 按 {diagnoseOnAudioReadKey} 诊断OnAudioRead回调问题");
        Debug.Log($"  - 按 {forceRestartAudioSourceKey} 强制重启AudioSource");
    }
    
    void Update()
    {
        if (_audioSink == null) return;
        
        // 键盘快捷键
        if (Input.GetKeyDown(checkEnvironmentKey))
        {
            CheckAudioEnvironment();
        }
        
        if (Input.GetKeyDown(generateTestAudioKey))
        {
            GenerateTestAudio();
        }
        
        if (Input.GetKeyDown(showStatsKey))
        {
            ShowAudioStats();
        }

        if (Input.GetKeyDown(toggleGUIKey))
        {
            showGUI = !showGUI;
            Debug.Log($"[AudioDebugTool] GUI显示: {(showGUI ? "开启" : "关闭")}");
        }

        if (Input.GetKeyDown(prepareAudioSessionKey))
        {
            PrepareAudioSession();
        }

        if (Input.GetKeyDown(diagnoseAudioSourceKey))
        {
            DiagnoseAudioSource();
        }

        if (Input.GetKeyDown(generateLongTestAudioKey))
        {
            GenerateLongTestAudio();
        }

        if (Input.GetKeyDown(debugGenerateTestAudioKey))
        {
            DebugGenerateTestAudio();
        }

        if (Input.GetKeyDown(forceRestartAudioSourceKey))
        {
            ForceRestartAudioSource();
        }

        //if (Input.GetKeyDown(diagnoseOnAudioReadKey))
        //{
        //    DiagnoseOnAudioRead();
        //}
    }
    
    /// <summary>
    /// 延迟检查音频环境
    /// </summary>
    private void CheckEnvironmentDelayed()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 自动检查音频环境...");
            _audioSink.CheckAudioEnvironment();
        }
    }
    
    /// <summary>
    /// 检查音频环境
    /// </summary>
    public void CheckAudioEnvironment()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 手动检查音频环境...");
            _audioSink.CheckAudioEnvironment();
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法检查环境");
        }
    }
    
    /// <summary>
    /// 准备音频会话（手动触发AudioExtrasSink创建）
    /// </summary>
    public void PrepareAudioSession()
    {
        if (_audioManager != null)
        {
            try
            {
                Debug.Log("[AudioDebugTool] 手动准备音频会话...");
                var mediaSession = _audioManager.PrepareAudioSession();

                if (mediaSession != null)
                {
                    Debug.Log("[AudioDebugTool] 音频会话准备成功");

                    // 重新查找AudioExtrasSink
                    _audioSink = FindObjectOfType<AudioExtrasSink>();
                    if (_audioSink != null)
                    {
                        Debug.Log("[AudioDebugTool] ✅ AudioExtrasSink已成功创建");
                    }
                    else
                    {
                        Debug.LogWarning("[AudioDebugTool] AudioExtrasSink仍未找到");
                    }
                }
                else
                {
                    Debug.LogError("[AudioDebugTool] 音频会话准备失败");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AudioDebugTool] 准备音频会话异常: {ex.Message}");
            }
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioManager未找到，无法准备音频会话");
        }
    }

    /// <summary>
    /// 生成测试音频
    /// </summary>
    public void GenerateTestAudio()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 生成测试音频信号...");

            // 先检查AudioSource状态
            var audioSources = FindObjectsOfType<AudioSource>();
            var sipAudioSource = System.Array.Find(audioSources,
                s => s.gameObject.name.Contains("SIP") || (s.clip?.name.Contains("SIP") == true));

            if (sipAudioSource != null)
            {
                Debug.Log($"[AudioDebugTool] 测试前AudioSource状态:");
                Debug.Log($"  - 是否播放: {sipAudioSource.isPlaying}");
                Debug.Log($"  - 音量: {sipAudioSource.volume}");
                Debug.Log($"  - 是否静音: {sipAudioSource.mute}");
                Debug.Log($"  - AudioClip: {(sipAudioSource.clip != null ? sipAudioSource.clip.name : "null")}");

                if (sipAudioSource.clip != null)
                {
                    Debug.Log($"  - Clip采样率: {sipAudioSource.clip.frequency}Hz");
                    Debug.Log($"  - Clip长度: {sipAudioSource.clip.length:F2}秒");
                }

                // 确保AudioSource正在播放
                if (!sipAudioSource.isPlaying)
                {
                    Debug.LogWarning("[AudioDebugTool] AudioSource未播放，尝试启动...");
                    sipAudioSource.Play();
                    Debug.Log($"[AudioDebugTool] 启动后播放状态: {sipAudioSource.isPlaying}");
                }
            }
            else
            {
                Debug.LogError("[AudioDebugTool] 未找到SIP相关的AudioSource");
            }

            // 生成多个测试音频帧以增加持续时间
            Debug.Log("[AudioDebugTool] 生成连续测试音频（5个20ms帧 = 100ms）...");
            for (int i = 0; i < 5; i++)
            {
                _audioSink.GenerateTestAudio();
                Debug.Log($"[AudioDebugTool] 已生成第{i+1}个测试音频帧");
            }

            // 立即检查队列状态
            StartCoroutine(MonitorQueueStatus());

            // 等待一小段时间后检查结果
            StartCoroutine(CheckTestAudioResult());
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法生成测试音频");
        }
    }

    /// <summary>
    /// 监控队列状态变化
    /// </summary>
    private System.Collections.IEnumerator MonitorQueueStatus()
    {
        Debug.Log("[AudioDebugTool] 开始监控音频队列状态...");

        for (int i = 0; i < 20; i++) // 监控2秒（20 x 0.1秒）
        {
            if (_audioSink != null)
            {
                // 通过反射获取队列大小（因为_audioQueue是私有的）
                var audioSinkType = _audioSink.GetType();
                var queueField = audioSinkType.GetField("_currentQueueSize",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (queueField != null)
                {
                    var queueSize = queueField.GetValue(_audioSink);
                    Debug.Log($"[AudioDebugTool] T+{i * 0.1f:F1}s: 队列大小 = {queueSize}");
                }

                // 获取统计信息
                var statsField = audioSinkType.GetField("_stats",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (statsField != null)
                {
                    var stats = statsField.GetValue(_audioSink);
                    var statsType = stats.GetType();

                    var audioFrames = statsType.GetField("AudioFrames")?.GetValue(stats) ?? 0;
                    var silentFrames = statsType.GetField("SilentFrames")?.GetValue(stats) ?? 0;
                    var audioReadCalls = statsType.GetField("AudioReadCalls")?.GetValue(stats) ?? 0;

                    Debug.Log($"[AudioDebugTool] T+{i * 0.1f:F1}s: 播放={audioFrames}, 静音={silentFrames}, 回调={audioReadCalls}");
                }
            }

            yield return new WaitForSeconds(0.1f);
        }

        Debug.Log("[AudioDebugTool] 队列监控完成");
    }

    /// <summary>
    /// 检查测试音频结果
    /// </summary>
    private System.Collections.IEnumerator CheckTestAudioResult()
    {
        yield return new WaitForSeconds(2.5f); // 等待2.5秒，让监控完成

        Debug.Log("[AudioDebugTool] 测试音频结果检查:");
        if (_audioSink != null)
        {
            // 调用环境检查来显示统计信息
            _audioSink.CheckAudioEnvironment();
        }
    }

    /// <summary>
    /// 深度诊断AudioSource问题
    /// </summary>
    public void DiagnoseAudioSource()
    {
        Debug.Log("=== AudioSource深度诊断 ===");

        // 查找所有AudioSource
        var audioSources = FindObjectsOfType<AudioSource>();
        Debug.Log($"场景中总共有 {audioSources.Length} 个AudioSource");

        AudioSource sipAudioSource = null;

        foreach (var source in audioSources)
        {
            bool isSipRelated = source.gameObject.name.Contains("SIP") ||
                               (source.clip?.name.Contains("SIP") == true) ||
                               source.gameObject.GetComponent<AudioExtrasSink>() != null;

            Debug.Log($"AudioSource: {source.gameObject.name}");
            Debug.Log($"  - SIP相关: {isSipRelated}");
            Debug.Log($"  - 播放状态: {source.isPlaying}");
            Debug.Log($"  - 音量: {source.volume}");
            Debug.Log($"  - 静音: {source.mute}");
            Debug.Log($"  - 优先级: {source.priority}");
            Debug.Log($"  - 空间混合: {source.spatialBlend}");
            Debug.Log($"  - 循环: {source.loop}");

            if (source.clip != null)
            {
                Debug.Log($"  - AudioClip: {source.clip.name}");
                Debug.Log($"  - Clip采样率: {source.clip.frequency}Hz");
                Debug.Log($"  - Clip长度: {source.clip.length:F2}秒");
                Debug.Log($"  - Clip样本数: {source.clip.samples}");
                Debug.Log($"  - Clip声道: {source.clip.channels}");
                Debug.Log($"  - Clip加载状态: {source.clip.loadState}");
            }
            else
            {
                Debug.Log($"  - AudioClip: null");
            }

            if (isSipRelated)
            {
                sipAudioSource = source;
            }
        }

        // 专门诊断SIP AudioSource
        if (sipAudioSource != null)
        {
            Debug.Log("=== SIP AudioSource专项诊断 ===");

            // 检查AudioListener
            var audioListener = FindObjectOfType<AudioListener>();
            if (audioListener != null)
            {
                Debug.Log($"AudioListener: {audioListener.gameObject.name}");
                Debug.Log($"  - 启用状态: {audioListener.enabled}");
                Debug.Log($"  - 音量: {AudioListener.volume}");
                Debug.Log($"  - 暂停状态: {AudioListener.pause}");
            }
            else
            {
                Debug.LogError("未找到AudioListener！这会导致无法听到音频");
            }

            // 检查Unity音频设置
            Debug.Log("Unity音频系统状态:");
            Debug.Log($"  - 输出采样率: {AudioSettings.outputSampleRate}Hz");
            Debug.Log($"  - DSP缓冲区: {AudioSettings.GetConfiguration().dspBufferSize}");
            Debug.Log($"  - 扬声器模式: {AudioSettings.GetConfiguration().speakerMode}");

            // 尝试手动播放测试
            Debug.Log("尝试手动启动AudioSource播放...");
            if (!sipAudioSource.isPlaying)
            {
                sipAudioSource.Play();
                Debug.Log($"手动启动后播放状态: {sipAudioSource.isPlaying}");
            }

            // 检查OnAudioRead回调
            if (sipAudioSource.clip != null)
            {
                Debug.Log($"AudioClip类型: {sipAudioSource.clip.GetType().Name}");
                Debug.Log($"是否为程序生成的AudioClip: {sipAudioSource.clip.name == "SIPAudioPlayback"}");
            }
        }
        else
        {
            Debug.LogError("未找到SIP相关的AudioSource！");
        }

        Debug.Log("=== 诊断完成 ===");
    }

    /// <summary>
    /// 生成长时间测试音频（1秒持续播放）
    /// </summary>
    public void GenerateLongTestAudio()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 生成长时间测试音频（1秒）...");

            // 生成50个20ms的音频帧，总共1秒
            StartCoroutine(GenerateContinuousTestAudio());
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法生成长时间测试音频");
        }
    }

    /// <summary>
    /// 连续生成测试音频协程
    /// </summary>
    private System.Collections.IEnumerator GenerateContinuousTestAudio()
    {
        Debug.Log("[AudioDebugTool] 开始连续生成测试音频...");

        // 确保AudioSource正在播放
        var audioSources = FindObjectsOfType<AudioSource>();
        var sipAudioSource = System.Array.Find(audioSources,
            s => s.gameObject.name.Contains("SIP") || (s.clip?.name.Contains("SIP") == true));

        if (sipAudioSource != null && !sipAudioSource.isPlaying)
        {
            sipAudioSource.Play();
            Debug.Log("[AudioDebugTool] 已启动AudioSource播放");
        }

        // 生成50个20ms帧，每20ms生成一个
        for (int i = 0; i < 50; i++)
        {
            if (_audioSink != null)
            {
                _audioSink.GenerateTestAudio();

                // 每10帧输出一次状态
                if (i % 10 == 0)
                {
                    Debug.Log($"[AudioDebugTool] 已生成 {i + 1}/50 个测试音频帧");

                    // 获取当前队列状态
                    var audioSinkType = _audioSink.GetType();
                    var queueField = audioSinkType.GetField("_currentQueueSize",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (queueField != null)
                    {
                        var queueSize = queueField.GetValue(_audioSink);
                        Debug.Log($"[AudioDebugTool] 当前队列大小: {queueSize}");
                    }
                }
            }

            yield return new WaitForSeconds(0.02f); // 等待20ms
        }

        Debug.Log("[AudioDebugTool] 连续测试音频生成完成，总时长1秒");

        // 等待2秒后检查最终结果
        yield return new WaitForSeconds(2.0f);

        Debug.Log("[AudioDebugTool] 长时间测试音频结果检查:");
        if (_audioSink != null)
        {
            _audioSink.CheckAudioEnvironment();
        }
    }

    /// <summary>
    /// 调试单个测试音频生成过程
    /// </summary>
    public void DebugGenerateTestAudio()
    {
        if (_audioSink != null)
        {
            Debug.Log("=== 调试单个测试音频生成 ===");

            // 检查AudioExtrasSink状态
            Debug.Log($"AudioExtrasSink对象: {_audioSink.GetType().Name}");

            // 使用反射获取内部状态
            var audioSinkType = _audioSink.GetType();

            // 检查_isStarted状态
            var isStartedField = audioSinkType.GetField("_isStarted",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (isStartedField != null)
            {
                var isStarted = isStartedField.GetValue(_audioSink);
                Debug.Log($"_isStarted: {isStarted}");
            }

            // 检查重采样设置
            var needsResamplingField = audioSinkType.GetField("_needsResampling",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (needsResamplingField != null)
            {
                var needsResampling = needsResamplingField.GetValue(_audioSink);
                Debug.Log($"_needsResampling: {needsResampling}");
            }

            // 检查采样率设置
            var unityPlaybackSampleRateField = audioSinkType.GetField("_unityPlaybackSampleRate",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (unityPlaybackSampleRateField != null)
            {
                var unityPlaybackSampleRate = unityPlaybackSampleRateField.GetValue(_audioSink);
                Debug.Log($"_unityPlaybackSampleRate: {unityPlaybackSampleRate}Hz");
            }

            // 获取队列初始状态
            var queueSizeField = audioSinkType.GetField("_currentQueueSize",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (queueSizeField != null)
            {
                var queueSize = queueSizeField.GetValue(_audioSink);
                Debug.Log($"生成前队列大小: {queueSize}");
            }

            Debug.Log("调用GenerateTestAudio()...");

            try
            {
                // 调用GenerateTestAudio
                _audioSink.GenerateTestAudio();
                Debug.Log("GenerateTestAudio()调用成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"GenerateTestAudio()调用失败: {ex.Message}");
                Debug.LogError($"异常堆栈: {ex.StackTrace}");
            }

            // 等待一小段时间后检查队列状态
            StartCoroutine(CheckQueueAfterGenerate());
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法调试测试音频生成");
        }
    }

    /// <summary>
    /// 检查生成后的队列状态 - 实时监控版本
    /// </summary>
    private System.Collections.IEnumerator CheckQueueAfterGenerate()
    {
        Debug.Log("开始实时监控队列状态变化...");

        if (_audioSink != null)
        {
            var audioSinkType = _audioSink.GetType();
            var queueSizeField = audioSinkType.GetField("_currentQueueSize",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var statsField = audioSinkType.GetField("_stats",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // 立即检查（0ms后）
            if (queueSizeField != null)
            {
                var queueSize = queueSizeField.GetValue(_audioSink);
                Debug.Log($"T+0ms: 队列大小 = {queueSize}");
            }

            // 每10ms检查一次，持续200ms
            for (int i = 1; i <= 20; i++)
            {
                yield return new WaitForSeconds(0.01f); // 等待10ms

                if (queueSizeField != null)
                {
                    var queueSize = queueSizeField.GetValue(_audioSink);
                    Debug.Log($"T+{i * 10}ms: 队列大小 = {queueSize}");

                    if ((int)queueSize > 0)
                    {
                        Debug.Log($"✅ T+{i * 10}ms: 队列中有数据！");
                    }
                }

                // 每50ms检查一次统计信息
                if (i % 5 == 0 && statsField != null)
                {
                    var stats = statsField.GetValue(_audioSink);
                    var statsType = stats.GetType();

                    var audioFrames = statsType.GetField("AudioFrames")?.GetValue(stats) ?? 0;
                    var silentFrames = statsType.GetField("SilentFrames")?.GetValue(stats) ?? 0;
                    var audioReadCalls = statsType.GetField("AudioReadCalls")?.GetValue(stats) ?? 0;

                    Debug.Log($"T+{i * 10}ms: 播放={audioFrames}, 静音={silentFrames}, 回调={audioReadCalls}");
                }
            }

            // 最终检查
            if (queueSizeField != null)
            {
                var queueSize = queueSizeField.GetValue(_audioSink);
                Debug.Log($"最终队列大小: {queueSize}");

                if ((int)queueSize > 0)
                {
                    Debug.Log("✅ 测试音频仍在队列中");
                }
                else
                {
                    Debug.Log("📊 测试音频已被完全消耗（这可能是正常的）");
                }
            }

            // 最终统计信息
            if (statsField != null)
            {
                var stats = statsField.GetValue(_audioSink);
                var statsType = stats.GetType();

                var audioFrames = statsType.GetField("AudioFrames")?.GetValue(stats) ?? 0;
                var silentFrames = statsType.GetField("SilentFrames")?.GetValue(stats) ?? 0;
                var audioReadCalls = statsType.GetField("AudioReadCalls")?.GetValue(stats) ?? 0;

                Debug.Log($"最终统计: 播放={audioFrames}, 静音={silentFrames}, 回调={audioReadCalls}");

                if ((int)audioFrames > 0)
                {
                    Debug.Log("🎉 成功！测试音频已被播放！");
                }
                else
                {
                    Debug.LogWarning("⚠️ 测试音频未被计入播放统计");
                }
            }
        }

        Debug.Log("=== 实时监控完成 ===");
    }
    
    /// <summary>
    /// 强制重启AudioSource
    /// </summary>
    public void ForceRestartAudioSource()
    {
        if (_audioSink != null)
        {
            Debug.Log("[AudioDebugTool] 强制重启AudioSource...");
            _audioSink.ForceRestartAudioSource();
        }
        else
        {
            Debug.LogError("[AudioDebugTool] AudioExtrasSink未找到，无法重启AudioSource");
        }
    }

    /// <summary>
    /// 显示音频统计信息
    /// </summary>
    public void ShowAudioStats()
    {
        Debug.Log("[AudioDebugTool] 音频统计信息:");
        
        // Unity音频设置
        Debug.Log($"  Unity音频设置:");
        Debug.Log($"    - 输出采样率: {AudioSettings.outputSampleRate}Hz");
        Debug.Log($"    - DSP缓冲区: {AudioSettings.GetConfiguration().dspBufferSize}");
        Debug.Log($"    - 扬声器模式: {AudioSettings.GetConfiguration().speakerMode}");
        
        // AudioManager状态
        if (_audioManager != null)
        {
            Debug.Log($"  AudioManager状态:");
            Debug.Log($"    - 音频会话已启动: {_audioManager.IsAudioSessionStarted()}");
        }
        
        // AudioSource状态
        var audioSources = FindObjectsOfType<AudioSource>();
        Debug.Log($"  场景中的AudioSource数量: {audioSources.Length}");
        
        foreach (var source in audioSources)
        {
            if (source.gameObject.name.Contains("SIP") || source.clip?.name.Contains("SIP") == true)
            {
                Debug.Log($"    SIP AudioSource: {source.gameObject.name}");
                Debug.Log($"      - 播放状态: {source.isPlaying}");
                Debug.Log($"      - 音量: {source.volume}");
                Debug.Log($"      - 优先级: {source.priority}");
                if (source.clip != null)
                {
                    Debug.Log($"      - Clip: {source.clip.name}, {source.clip.frequency}Hz, {source.clip.length:F2}s");
                }
            }
        }
    }
    
    /// <summary>
    /// GUI显示调试信息
    /// </summary>
    void OnGUI()
    {
        if (!enableDebugLogs || !showGUI) return;

        // 限制GUI更新频率以提高性能
        if (Time.realtimeSinceStartup - _lastGUIUpdate < guiUpdateInterval) return;
        _lastGUIUpdate = Time.realtimeSinceStartup;

        // 设置GUI样式
        GUIStyle titleStyle = new GUIStyle(GUI.skin.label);
        titleStyle.fontSize = 16;
        titleStyle.fontStyle = FontStyle.Bold;
        titleStyle.normal.textColor = Color.white;

        GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
        buttonStyle.fontSize = 12;

        GUIStyle labelStyle = new GUIStyle(GUI.skin.label);
        labelStyle.fontSize = 11;
        labelStyle.normal.textColor = Color.cyan;

        GUILayout.BeginArea(new Rect(10, 10, 320, 250));
        GUILayout.BeginVertical("box");

        GUILayout.Label("🎵 音频调试工具", titleStyle);

        GUILayout.Space(10);
        
        if (GUILayout.Button("🔍 检查音频环境 (F1)", buttonStyle))
        {
            CheckAudioEnvironment();
        }

        if (GUILayout.Button("🎵 生成测试音频 (F2)", buttonStyle))
        {
            GenerateTestAudio();
        }

        if (GUILayout.Button("📊 显示统计信息 (F3)", buttonStyle))
        {
            ShowAudioStats();
        }

        if (GUILayout.Button("🔧 准备音频会话 (F5)", buttonStyle))
        {
            PrepareAudioSession();
        }

        if (GUILayout.Button("🔬 深度诊断AudioSource (F6)", buttonStyle))
        {
            DiagnoseAudioSource();
        }

        if (GUILayout.Button("🎵 长时间测试音频 (F7)", buttonStyle))
        {
            GenerateLongTestAudio();
        }

        if (GUILayout.Button("🐛 调试测试音频生成 (F8)", buttonStyle))
        {
            DebugGenerateTestAudio();
        }

        GUILayout.Space(10);

        // 显示基本状态
        GUILayout.Label($"🎛️ Unity采样率: {AudioSettings.outputSampleRate}Hz", labelStyle);

        if (_audioManager != null)
        {
            bool sessionStarted = _audioManager.IsAudioSessionStarted();
            string sessionStatus = sessionStarted ? "✅ 已启动" : "❌ 未启动";
            GUIStyle sessionStyle = new GUIStyle(labelStyle);
            sessionStyle.normal.textColor = sessionStarted ? Color.green : Color.red;
            GUILayout.Label($"🔗 音频会话: {sessionStatus}", sessionStyle);
        }

        var sipAudioSources = System.Array.FindAll(FindObjectsOfType<AudioSource>(),
            s => s.gameObject.name.Contains("SIP") || (s.clip?.name.Contains("SIP") == true));
        GUILayout.Label($"🔊 SIP AudioSource: {sipAudioSources.Length}个", labelStyle);

        if (sipAudioSources.Length > 0)
        {
            var source = sipAudioSources[0];
            bool isPlaying = source.isPlaying;
            string playStatus = isPlaying ? "▶️ 播放中" : "⏹️ 已停止";
            GUIStyle playStyle = new GUIStyle(labelStyle);
            playStyle.normal.textColor = isPlaying ? Color.green : Color.yellow;
            GUILayout.Label($"📻 播放状态: {playStatus}", playStyle);
            GUILayout.Label($"🔊 音量: {source.volume:F2}", labelStyle);
        }
        
        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
